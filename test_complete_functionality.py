#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
完整功能测试 - 验证所有新功能
"""

import sys
import time
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from api_requests.binance.binance_orderbook_requester import BinanceOrderbookRequester

def test_dynamic_symbol_discovery():
    """测试动态交易对发现功能"""
    print("🔍 测试动态交易对发现功能...")
    
    requester = BinanceOrderbookRequester()
    
    # 测试获取活跃合约交易对
    print("📈 获取活跃合约交易对...")
    futures_symbols = requester.get_active_futures_symbols(max_symbols=10)
    print(f"✅ 获取到 {len(futures_symbols)} 个合约交易对")
    print(f"前5个: {futures_symbols[:5]}")
    
    # 测试获取活跃现货交易对
    print("\n📉 获取活跃现货交易对...")
    spot_symbols = requester.get_active_spot_symbols(max_symbols=10)
    print(f"✅ 获取到 {len(spot_symbols)} 个现货交易对")
    print(f"前5个: {spot_symbols[:5]}")
    
    return len(futures_symbols) > 0 and len(spot_symbols) > 0

def test_single_orderbook_fetch():
    """测试单个订单簿获取"""
    print("\n📊 测试单个订单簿获取...")
    
    requester = BinanceOrderbookRequester()
    
    # 测试合约订单簿
    print("📈 获取BTCUSDT合约订单簿...")
    futures_success = requester.fetch_futures_orderbook("BTCUSDT", 50)
    print(f"合约结果: {'✅ 成功' if futures_success else '❌ 失败'}")
    
    # 测试现货订单簿
    print("📉 获取ETHUSDT现货订单簿...")
    spot_success = requester.fetch_spot_orderbook("ETHUSDT", 50)
    print(f"现货结果: {'✅ 成功' if spot_success else '❌ 失败'}")
    
    return futures_success and spot_success

def test_batch_orderbook_fetch():
    """测试批量订单簿获取"""
    print("\n📦 测试批量订单簿获取...")
    
    requester = BinanceOrderbookRequester()
    
    # 测试批量合约获取
    print("📈 批量获取合约订单簿（3个交易对）...")
    futures_result = requester.fetch_all_active_futures_orderbooks(
        max_symbols=3,
        batch_size=2,
        limit=50
    )
    
    print(f"合约批量结果: {futures_result.get('success_count', 0)}/{futures_result.get('total_symbols', 0)} 成功")
    
    # 测试批量现货获取
    print("📉 批量获取现货订单簿（2个交易对）...")
    spot_result = requester.fetch_all_active_spot_orderbooks(
        max_symbols=2,
        batch_size=2,
        limit=50
    )
    
    print(f"现货批量结果: {spot_result.get('success_count', 0)}/{spot_result.get('total_symbols', 0)} 成功")
    
    return (futures_result.get('success', False) and 
            spot_result.get('success', False))

def test_data_storage_structure():
    """测试数据存储结构"""
    print("\n📁 测试数据存储结构...")
    
    # 检查目录结构
    base_path = Path("data_storage/binance/binance_orderbook")
    
    futures_path = base_path / "binance_orderbook_futures"
    spot_path = base_path / "binance_orderbook_spot"
    
    print(f"合约目录存在: {'✅' if futures_path.exists() else '❌'}")
    print(f"现货目录存在: {'✅' if spot_path.exists() else '❌'}")
    
    # 检查是否有数据文件
    futures_dirs = list(futures_path.glob("binance_orderbook_futures_*")) if futures_path.exists() else []
    spot_dirs = list(spot_path.glob("binance_orderbook_spot_*")) if spot_path.exists() else []
    
    print(f"合约交易对目录数量: {len(futures_dirs)}")
    print(f"现货交易对目录数量: {len(spot_dirs)}")
    
    # 检查最新的数据文件
    if futures_dirs:
        latest_futures_dir = max(futures_dirs, key=lambda x: x.stat().st_mtime)
        futures_files = list(latest_futures_dir.glob("*.json"))
        print(f"最新合约目录: {latest_futures_dir.name}")
        print(f"合约数据文件数量: {len(futures_files)}")
    
    if spot_dirs:
        latest_spot_dir = max(spot_dirs, key=lambda x: x.stat().st_mtime)
        spot_files = list(latest_spot_dir.glob("*.json"))
        print(f"最新现货目录: {latest_spot_dir.name}")
        print(f"现货数据文件数量: {len(spot_files)}")
    
    return len(futures_dirs) > 0 and len(spot_dirs) > 0

def main():
    """主测试函数"""
    print("🧪 开始完整功能测试")
    print("=" * 60)
    
    test_results = {}
    
    # 测试1: 动态交易对发现
    test_results['dynamic_discovery'] = test_dynamic_symbol_discovery()
    
    # 测试2: 单个订单簿获取
    test_results['single_fetch'] = test_single_orderbook_fetch()
    
    # 测试3: 批量订单簿获取
    test_results['batch_fetch'] = test_batch_orderbook_fetch()
    
    # 测试4: 数据存储结构
    test_results['storage_structure'] = test_data_storage_structure()
    
    # 总结测试结果
    print("\n" + "=" * 60)
    print("📋 测试结果总结:")
    print("=" * 60)
    
    for test_name, result in test_results.items():
        status = "✅ 通过" if result else "❌ 失败"
        test_display_name = {
            'dynamic_discovery': '动态交易对发现',
            'single_fetch': '单个订单簿获取',
            'batch_fetch': '批量订单簿获取',
            'storage_structure': '数据存储结构'
        }.get(test_name, test_name)
        
        print(f"{test_display_name}: {status}")
    
    # 计算总体成功率
    success_count = sum(1 for result in test_results.values() if result)
    total_tests = len(test_results)
    success_rate = (success_count / total_tests) * 100
    
    print(f"\n🎯 总体成功率: {success_count}/{total_tests} ({success_rate:.1f}%)")
    
    if success_rate == 100:
        print("🎉 所有测试通过！系统已成功实现动态获取所有币种的订单簿数据功能！")
    else:
        print("⚠️ 部分测试失败，请检查相关功能")
    
    print("=" * 60)

if __name__ == "__main__":
    main()
