#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
CoinGlass CDRI指数数据请求器
获取CDRI（CoinGlass衍生品风险指数）历史数据
"""

import sys
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from main import FileManager, HTTPClient, setup_logger
from config import COINGLASS_CONFIG

class CoinglassCDRIIndexRequester:
    """CoinGlass CDRI指数数据请求器"""

    def __init__(self):
        """初始化请求器"""
        self.api_identifier = "coinglass_get_cdri_index"
        self.logger = setup_logger(f"api_requests.{self.api_identifier}")
        self.file_manager = FileManager()
        
        # 创建HTTP客户端
        self.http_client = HTTPClient(
            timeout=COINGLASS_CONFIG["timeout"],
            max_retries=COINGLASS_CONFIG["max_retries"]
        )
        
        # 设置CoinGlass API认证头
        self.headers = {
            "accept": "application/json",
            "CG-API-KEY": COINGLASS_CONFIG["api_key"]
        }
        
        # API配置
        self.base_url = COINGLASS_CONFIG["base_url"]
        self.endpoint = "/api/futures/cdri-index/history"

    def get_cdri_data(self):
        """
        获取CDRI指数历史数据
        
        Returns:
            bool: 是否成功获取数据
        """
        try:
            self.logger.info(f"🚀 开始获取CoinGlass CDRI指数历史数据...")
            
            # 构建请求URL
            url = f"{self.base_url}{self.endpoint}"
            
            # 发送请求
            success, response, message = self.http_client.get(
                url=url,
                headers=self.headers
            )
            
            if not success or not response:
                self.logger.error(f"❌ 获取数据失败：{message}")
                return False
            
            # 验证响应格式
            if not isinstance(response, dict) or response.get("code") != "0":
                error_msg = response.get("msg", "未知错误") if isinstance(response, dict) else "响应格式错误"
                self.logger.error(f"❌ API返回错误: {error_msg}")
                return False
            
            # 提取数据
            data_list = response.get("data", [])
            if not data_list:
                self.logger.warning("⚠️ 返回的数据为空")
                return False
            
            # 计算统计信息
            total_records = len(data_list)
            
            # 获取时间范围
            if total_records > 0:
                first_record = data_list[0]
                last_record = data_list[-1]
                start_time = first_record.get("time")
                end_time = last_record.get("time")
            else:
                start_time = end_time = None
            
            # 构建元数据
            metadata = {
                "request_url": url,
                "records_count": total_records,
                "data_type": "cdri_index_history",
                "start_time": start_time,
                "end_time": end_time,
                "update_frequency": "实时更新",
                "api_level_required": "爱好版及以上",
                "description": "CoinGlass衍生品风险指数历史数据"
            }
            
            # 保存数据
            save_success, file_path, save_message = self.file_manager.save_api_data(
                api_identifier=self.api_identifier,
                data=response,
                metadata=metadata
            )
            
            if save_success:
                self.logger.info(f"✅ 成功获取并保存 {total_records} 条CDRI指数历史记录")
                return True
            else:
                self.logger.error(f"❌ 数据保存失败: {save_message}")
                return False
                
        except Exception as e:
            self.logger.error(f"❌ 获取CDRI指数数据时发生异常: {str(e)}")
            return False

def main():
    """主函数"""
    requester = CoinglassCDRIIndexRequester()
    success = requester.get_cdri_data()
    
    if success:
        print("CoinGlass CDRI指数数据获取成功")
    else:
        print("CoinGlass CDRI指数数据获取失败")
        sys.exit(1)

if __name__ == "__main__":
    main()
