#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
CoinGlass 恐惧与贪婪指数数据请求器
获取加密货币市场恐惧与贪婪指数历史数据
"""

import sys
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from main import FileManager, HTTPClient, setup_logger
from config import COINGLASS_CONFIG

class CoinglassFearGreedIndexRequester:
    """CoinGlass 恐惧与贪婪指数数据请求器"""

    def __init__(self):
        """初始化请求器"""
        self.api_identifier = "coinglass_get_fear_greed_index"
        self.logger = setup_logger(f"api_requests.{self.api_identifier}")
        self.file_manager = FileManager()
        
        # 创建HTTP客户端
        self.http_client = HTTPClient(
            timeout=COINGLASS_CONFIG["timeout"],
            max_retries=COINGLASS_CONFIG["max_retries"]
        )
        
        # 设置CoinGlass API认证头
        self.headers = {
            "accept": "application/json",
            "CG-API-KEY": COINGLASS_CONFIG["api_key"]
        }
        
        # API配置
        self.base_url = COINGLASS_CONFIG["base_url"]
        self.endpoint = "/api/index/fear-greed-history"

    def get_fear_greed_data(self):
        """
        获取恐惧与贪婪指数数据
        
        Returns:
            bool: 是否成功获取数据
        """
        try:
            self.logger.info(f"🚀 开始获取恐惧与贪婪指数数据...")
            
            # 构建请求URL
            url = f"{self.base_url}{self.endpoint}"
            
            # 发送请求
            success, response, message = self.http_client.get(
                url=url,
                headers=self.headers
            )
            
            if not success or not response:
                self.logger.error(f"❌ 获取数据失败：{message}")
                return False
            
            # 验证响应格式
            if not isinstance(response, dict) or response.get("code") != "0":
                error_msg = response.get("msg", "未知错误") if isinstance(response, dict) else "响应格式错误"
                self.logger.error(f"❌ API返回错误: {error_msg}")
                return False
            
            # 提取数据 - 恐惧与贪婪指数的数据结构特殊
            data = response.get("data", {})
            self.logger.info(f"🔍 API返回数据类型: {type(data)}")
            self.logger.info(f"🔍 API返回数据键: {list(data.keys()) if isinstance(data, dict) else 'Not a dict'}")

            if not data:
                self.logger.warning("⚠️ 返回的数据为空")
                return False

            # 恐惧与贪婪指数数据结构: data 直接包含 values, price, time_list 数组
            if isinstance(data, list) and len(data) > 0:
                data_item = data[0]
            else:
                data_item = data
            values = data_item.get("data_list", [])
            prices = data_item.get("price_list", [])
            time_list = data_item.get("time_list", [])

            self.logger.info(f"📊 数据结构检查: values={len(values)}, prices={len(prices)}, time_list={len(time_list)}")

            # 计算统计信息
            total_records = len(values)

            # 获取时间范围和最新指数
            if total_records > 0:
                start_timestamp = time_list[0] if time_list else None
                end_timestamp = time_list[-1] if time_list else None
                latest_value = values[-1] if values else None
                latest_price = prices[-1] if prices else None

                # 根据数值判断分类
                if latest_value is not None:
                    if latest_value <= 24:
                        latest_classification = "极度恐惧"
                    elif latest_value <= 49:
                        latest_classification = "恐惧"
                    elif latest_value <= 74:
                        latest_classification = "贪婪"
                    else:
                        latest_classification = "极度贪婪"
                else:
                    latest_classification = None
            else:
                start_timestamp = end_timestamp = latest_value = latest_price = latest_classification = None
            
            # 构建元数据
            metadata = {
                "request_url": url,
                "records_count": total_records,
                "data_type": "fear_greed_index_history",
                "start_timestamp": start_timestamp,
                "end_timestamp": end_timestamp,
                "latest_value": latest_value,
                "latest_price": latest_price,
                "latest_classification": latest_classification,
                "update_frequency": "每天一次",
                "api_level_required": "免费版及以上",
                "description": "加密货币市场恐惧与贪婪指数历史数据",
                "data_structure": "arrays: values[], price[], time_list[]",
                "value_ranges": {
                    "0-24": "极度恐惧",
                    "25-49": "恐惧",
                    "50-74": "贪婪",
                    "75-100": "极度贪婪"
                }
            }
            
            # 保存数据
            save_success, file_path, save_message = self.file_manager.save_api_data(
                api_identifier=self.api_identifier,
                data=response,
                metadata=metadata
            )
            
            if save_success:
                self.logger.info(f"✅ 成功获取并保存 {total_records} 条恐惧与贪婪指数记录")
                if latest_value is not None and latest_classification:
                    self.logger.info(f"📊 最新指数: {latest_value:.2f} ({latest_classification})")
                if latest_price is not None:
                    self.logger.info(f"💰 对应价格: ${latest_price:.2f}")
                return True
            else:
                self.logger.error(f"❌ 数据保存失败: {save_message}")
                return False
                
        except Exception as e:
            import traceback
            self.logger.error(f"❌ 获取恐惧与贪婪指数数据时发生异常: {str(e)}")
            self.logger.error(f"异常类型: {type(e).__name__}")
            self.logger.error(f"异常详情: {traceback.format_exc()}")
            return False

def main():
    """主函数"""
    requester = CoinglassFearGreedIndexRequester()
    success = requester.get_fear_greed_data()
    
    if success:
        print("恐惧与贪婪指数数据获取成功")
    else:
        print("恐惧与贪婪指数数据获取失败")
        sys.exit(1)

if __name__ == "__main__":
    main()
