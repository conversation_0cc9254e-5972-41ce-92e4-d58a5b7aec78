#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
订阅管理器 - 每日50积分扣费模式
简化的订阅逻辑：有积分就有服务，没积分就停服
"""

import json
import asyncio
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional
from pathlib import Path
import os

logger = logging.getLogger(__name__)

# 北京时间工具函数
def get_beijing_time():
    """获取北京时间"""
    beijing_tz = timezone(timedelta(hours=8))
    return datetime.now(beijing_tz)

def beijing_time_isoformat():
    """获取北京时间的ISO格式字符串"""
    return get_beijing_time().isoformat()

def format_beijing_time(dt_str, format_str="%Y-%m-%d %H:%M:%S"):
    """将ISO格式的时间字符串转换为北京时间并格式化"""
    try:
        # 如果输入的是ISO格式字符串，先解析
        if isinstance(dt_str, str):
            dt = datetime.fromisoformat(dt_str.replace('Z', '+00:00'))
        else:
            dt = dt_str
        
        # 如果没有时区信息，假设是UTC
        if dt.tzinfo is None:
            dt = dt.replace(tzinfo=timezone.utc)
        
        # 转换为北京时间
        beijing_tz = timezone(timedelta(hours=8))
        beijing_dt = dt.astimezone(beijing_tz)
        
        return beijing_dt.strftime(format_str)
    except Exception as e:
        logger.error(f"时间格式化失败: {e}")
        return str(dt_str)


# 数据文件配置 - 与主文件保持一致
BASE_DIR = os.path.dirname(os.path.abspath(__file__))
DATA_DIR = os.path.join(BASE_DIR, "data")

# 确保数据目录存在
os.makedirs(DATA_DIR, exist_ok=True)


def get_standard_new_user_data(user_id: int, username: str = "") -> Dict:
    """获取标准的新用户数据结构"""
    return {
        "user_id": user_id,
        "username": username,
        "points": 20,
        "total_recharged": 0,
        "register_time": beijing_time_isoformat(),
        "last_active": beijing_time_isoformat(),
        "subscription_active": False,
        "subscription_types": [],
        "subscription": {
            "is_active": False,
            "last_charge_date": None,
            "daily_cost": 50,
            "auto_renew": True
        },
        "alerts_received": {
            "ai": 0,
            "transfer": 0,
            "total": 0,
            "open_interest": 0,
            "rsi": 0,
            "funding_rate": 0
        },
        "last_alert_time": None
    }

class DailySubscriptionManager:
    """每日扣费订阅管理器"""
    
    DAILY_COST = 50  # 每日消耗积分
    
    def __init__(self, user_data_file: str = None):
        if user_data_file is None:
            user_data_file = os.path.join(DATA_DIR, "unified_users.json")
        self.user_data_file = user_data_file
        self._ensure_data_file()
    
    def _ensure_data_file(self):
        """确保数据文件存在 - 安全版本"""
        Path(self.user_data_file).parent.mkdir(parents=True, exist_ok=True)

        if not Path(self.user_data_file).exists():
            # 尝试从备份恢复
            backup_dir = Path(self.user_data_file).parent / "backups"
            if backup_dir.exists():
                backup_files = list(backup_dir.glob("**/user_data_*.json"))
                if backup_files:
                    latest_backup = max(backup_files, key=lambda x: x.stat().st_mtime)
                    try:
                        with open(latest_backup, 'r', encoding='utf-8') as f:
                            data = json.load(f)
                        if isinstance(data, dict) and len(data) > 0:
                            import shutil
                            shutil.copy2(latest_backup, self.user_data_file)
                            print(f"⚠️ 从备份恢复用户数据: {latest_backup}")
                            return
                    except Exception as e:
                        print(f"备份恢复失败: {e}")

            print("❌ 用户数据文件不存在且无法从备份恢复")
            print("❌ 拒绝创建空的用户数据文件以防数据丢失")
            raise FileNotFoundError(f"用户数据文件不存在: {self.user_data_file}")
    
    def _load_data(self) -> Dict:
        """加载用户数据"""
        try:
            with open(self.user_data_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        except (FileNotFoundError, json.JSONDecodeError):
            return {}
    
    def _save_data(self, data: Dict):
        """保存用户数据"""
        with open(self.user_data_file, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
    
    def get_user_subscription_status(self, user_id: int) -> Dict:
        """获取用户订阅状态（兼容user_data.json格式）"""
        data = self._load_data()
        user_key = str(user_id)
        
        if user_key not in data:
            # 新用户默认状态
            return {
                "is_active": False,
                "points": 0,
                "last_charge_date": None,
                "days_remaining": 0
            }
        
        user = data[user_key]
        
        # 兼容两种数据格式：
        # 1. zfb格式: {"points": 4000, "user_id": 123, ...}
        # 2. subscription格式: {"payment": {"points": 4000}, "subscription": {...}}
        if "payment" in user:
            # 新格式
            points = user.get("payment", {}).get("points", 0)
            subscription = user.get("subscription", {})
        else:
            # zfb格式
            points = user.get("points", 0)
            subscription = user.get("subscription", {})
        
        # 检查是否需要今日扣费
        today = format_beijing_time(get_beijing_time().isoformat(), "%Y-%m-%d")
        last_charge = subscription.get("last_charge_date")
        
        is_active = points >= self.DAILY_COST
        days_remaining = points // self.DAILY_COST
        
        return {
            "is_active": is_active,
            "points": points,
            "last_charge_date": last_charge,
            "days_remaining": days_remaining,
            "needs_charge_today": last_charge != today and is_active
        }
    
    def process_daily_charge(self, user_id: int) -> Dict:
        """处理每日扣费（兼容user_data.json格式）"""
        data = self._load_data()
        user_key = str(user_id)
        today = format_beijing_time(get_beijing_time().isoformat(), "%Y-%m-%d")
        
        if user_key not in data:
            return {"success": False, "message": "用户不存在"}
        
        user = data[user_key]
        
        # 兼容两种数据格式
        if "payment" in user:
            # 新格式
            points = user.get("payment", {}).get("points", 0)
        else:
            # zfb格式
            points = user.get("points", 0)
            
        subscription = user.setdefault("subscription", {})
        
        # 检查是否已扣费
        if subscription.get("last_charge_date") == today:
            return {
                "success": True, 
                "message": "今日已扣费",
                "points_after": points,
                "charged": False
            }
        
        # 检查积分是否足够
        if points < self.DAILY_COST:
            subscription["is_active"] = False
            subscription["last_charge_date"] = today
            self._save_data(data)
            
            return {
                "success": False,
                "message": f"积分不足，需要{self.DAILY_COST}积分，当前{points}积分",
                "points_after": points,
                "charged": False
            }
        
        # 扣除积分
        new_points = points - self.DAILY_COST
        
        # 更新积分（兼容两种格式）
        if "payment" in user:
            user["payment"]["points"] = new_points
            user["payment"]["last_active"] = beijing_time_isoformat()
        else:
            # zfb格式
            user["points"] = new_points
            user["last_active"] = beijing_time_isoformat()
        
        # 更新订阅状态
        subscription.update({
            "is_active": True,
            "last_charge_date": today,
            "daily_cost": self.DAILY_COST,
            "auto_renew": True
        })
        
        # 添加积分变动记录
        self._add_points_history(user_id, -self.DAILY_COST, "每日信号订阅", points, new_points)
        
        self._save_data(data)
        
        logger.info(f"用户 {user_id} 每日扣费成功: -{self.DAILY_COST}积分，余额: {new_points}")
        
        return {
            "success": True,
            "message": f"扣费成功，消耗{self.DAILY_COST}积分",
            "points_before": points,
            "points_after": new_points,
            "charged": True
        }
    
    def check_service_access(self, user_id: int) -> bool:
        """检查用户是否可以访问信号服务"""
        status = self.get_user_subscription_status(user_id)
        
        # 如果今天需要扣费，先处理扣费
        if status.get("needs_charge_today", False):
            charge_result = self.process_daily_charge(user_id)
            return charge_result.get("success", False)
        
        return status["is_active"]
    
    def add_points_from_payment(self, user_id: int, points_added: int, reason: str = "USDT充值"):
        """用户充值后添加积分"""
        data = self._load_data()
        user_key = str(user_id)
        
        if user_key not in data:
            return {"success": False, "message": "用户不存在"}
        
        user = data[user_key]
        old_points = user.get("payment", {}).get("points", 0)
        new_points = old_points + points_added
        
        user["payment"]["points"] = new_points
        user["payment"]["last_active"] = beijing_time_isoformat()
        
        # 充值后如果积分足够，自动激活订阅
        if new_points >= self.DAILY_COST:
            subscription = user.setdefault("subscription", {})
            subscription["is_active"] = True
            subscription["auto_renew"] = True
        
        self._add_points_history(user_id, points_added, reason, old_points, new_points)
        self._save_data(data)
        
        logger.info(f"用户 {user_id} 积分充值: +{points_added}，余额: {old_points} -> {new_points}")
        
        return {
            "success": True,
            "points_before": old_points,
            "points_after": new_points,
            "service_activated": new_points >= self.DAILY_COST
        }
    
    def _add_points_history(self, user_id: int, change: int, reason: str, 
                           balance_before: int, balance_after: int):
        """添加积分变动记录"""
        history_file = os.path.join(DATA_DIR, "points_history.json")
        
        try:
            with open(history_file, 'r', encoding='utf-8') as f:
                history = json.load(f)
        except (FileNotFoundError, json.JSONDecodeError):
            history = []
        
        record = {
            "user_id": user_id,
            "change": change,
            "reason": reason,
            "balance_before": balance_before,
            "balance_after": balance_after,
            "timestamp": beijing_time_isoformat()
        }
        
        history.append(record)
        
        with open(history_file, 'w', encoding='utf-8') as f:
            json.dump(history, f, ensure_ascii=False, indent=2)
    
    async def daily_charge_all_users(self):
        """每日凌晨自动扣费所有用户"""
        data = self._load_data()
        charged_count = 0
        stopped_count = 0
        
        logger.info("开始执行每日自动扣费...")
        
        for user_id_str in data.keys():
            user_id = int(user_id_str)
            result = self.process_daily_charge(user_id)
            
            if result.get("charged", False):
                charged_count += 1
            elif not result.get("success", False):
                stopped_count += 1
        
        logger.info(f"每日扣费完成: 成功扣费 {charged_count} 人，停服 {stopped_count} 人")
        
        return {
            "charged_count": charged_count,
            "stopped_count": stopped_count,
            "total_users": len(data)
        }
    
    def get_subscription_stats(self) -> Dict:
        """获取订阅统计信息"""
        data = self._load_data()
        total_users = len(data)
        active_users = 0
        total_points = 0
        
        for user in data.values():
            points = user.get("payment", {}).get("points", 0)
            total_points += points
            
            if points >= self.DAILY_COST:
                active_users += 1
        
        return {
            "total_users": total_users,
            "active_subscribers": active_users,
            "inactive_users": total_users - active_users,
            "total_points_in_system": total_points,
            "daily_revenue_potential": active_users * self.DAILY_COST
        }


# 使用示例
async def main():
    """测试订阅管理器"""
    manager = DailySubscriptionManager()
    
    # 测试用户状态
    user_id = 6511182257
    status = manager.get_user_subscription_status(user_id)
    print(f"用户订阅状态: {status}")
    
    # 测试服务访问权限
    can_access = manager.check_service_access(user_id)
    print(f"可以访问服务: {can_access}")
    
    # 查看统计信息
    stats = manager.get_subscription_stats()
    print(f"订阅统计: {stats}")


if __name__ == "__main__":
    asyncio.run(main()) 