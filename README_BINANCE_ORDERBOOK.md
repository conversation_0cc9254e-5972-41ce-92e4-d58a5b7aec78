# 币安订单薄数据监控器

## 📋 功能概述

这是一个自动化的币安订单薄数据采集系统，支持持续监控和数据存储。

## 🆕 新功能特性 (v2.0)

- ✅ **全量活跃币种获取**: 自动获取所有活跃的USDT交易对（最多500个）
- ✅ **智能交易对筛选**: 基于24小时交易量自动排序，优先获取活跃度高的币种
- ✅ **动态币种发现**: 实时从币安API获取最新的可交易币种列表
- ✅ **大规模数据采集**: 支持同时获取数百个交易对的订单簿数据
- ✅ **灵活配置模式**: 支持全量自动模式和手动配置模式

## 🔄 获取模式

### 模式1: 全量活跃币种模式 (推荐)
- 自动从币安API获取所有活跃的USDT交易对
- 按24小时交易量排序，优先获取活跃度高的币种
- 默认设置：合约150个，现货100个
- 自动过滤掉已停止交易或问题币种

### 模式2: 手动配置模式
- 使用配置文件中预定义的交易对列表
- 适合只关注特定币种的场景
- 完全可控的交易对选择

## 🚀 快速启动

### 方法1：使用批处理文件（推荐）
```bash
# 双击运行
start_binance_monitor.bat
```

### 方法2：使用Python脚本
```bash
# 持续监控模式（默认）
python start_binance_orderbook_monitor.py

# 或者直接使用请求器
python api_requests/binance/binance_orderbook_requester.py --mode continuous
```

### 方法3：单次执行
```bash
# 获取单个交易对数据
python api_requests/binance/binance_orderbook_requester.py --mode single --symbol BTCUSDT --market futures --limit 50
```

## ⚙️ 配置说明

所有配置都在 `config.py` 文件中的 `BINANCE_ORDERBOOK_CONFIG` 部分：

```python
BINANCE_ORDERBOOK_CONFIG = {
    "enable_continuous_run": True,     # 启用持续运行
    "update_interval_seconds": 300,    # 更新间隔（秒）

    # 🆕 获取模式配置
    "fetch_mode": "all_active",        # "all_active" 获取所有活跃币种, "manual" 使用手动配置
    "max_futures_symbols": 150,        # 最大合约交易对数量
    "max_spot_symbols": 100,           # 最大现货交易对数量

    # 手动配置的交易对（当 fetch_mode = "manual" 时使用）
    "symbols": {
        "futures": [                   # 合约交易对列表
            "BTCUSDT", "ETHUSDT", "BNBUSDT", "ADAUSDT",
            "SOLUSDT", "DOGEUSDT", "XRPUSDT", "LINKUSDT",
            "AVAXUSDT", "DOTUSDT", "MATICUSDT", "LTCUSDT"
        ],
        "spot": [                      # 现货交易对列表
            "BTCUSDT", "ETHUSDT", "BNBUSDT", "ADAUSDT",
            "SOLUSDT", "DOGEUSDT", "XRPUSDT", "LINKUSDT"
        ]
    },

    # 订单薄配置
    "orderbook_limit": 50,             # 订单薄深度
    "futures_batch_size": 30,          # 合约批量处理大小
    "spot_batch_size": 20,             # 现货批量处理大小
    "delay_between_batches": 2,        # 批次间延迟（秒）
    "delay_between_symbols": 0.5,      # 单个交易对间延迟（秒）
}
```

### 🔧 配置参数说明

- **fetch_mode**:
  - `"all_active"`: 自动获取所有活跃币种（推荐）
  - `"manual"`: 使用手动配置的币种列表
- **max_futures_symbols**: 全量模式下最大合约交易对数量
- **max_spot_symbols**: 全量模式下最大现货交易对数量
- **futures_batch_size**: 合约批处理大小，避免API限制
- **spot_batch_size**: 现货批处理大小，避免API限制

## 📁 数据存储结构

数据按以下结构存储：

```
data_storage/
└── binance/
    └── binance_orderbook/
        ├── binance_orderbook_futures/
        │   ├── binance_orderbook_futures_BTCUSDT/
        │   │   ├── 20250726_031722_binance_orderbook_futures_BTCUSDT.json
        │   │   └── ...
        │   ├── binance_orderbook_futures_ETHUSDT/
        │   └── ...
        └── binance_orderbook_spot/
            ├── binance_orderbook_spot_BTCUSDT/
            │   ├── 20250726_031722_binance_orderbook_spot_BTCUSDT.json
            │   └── ...
            └── ...
```

## 📊 数据格式

每个JSON文件包含：

```json
{
    "metadata": {
        "symbol": "BTCUSDT",
        "market_type": "futures",
        "limit": 50,
        "timestamp": "2025-07-26T03:17:22.665000",
        "api_endpoint": "/fapi/v1/depth"
    },
    "data": {
        "lastUpdateId": 123456789,
        "bids": [
            ["67000.00", "1.234"],
            ["66999.99", "0.567"]
        ],
        "asks": [
            ["67000.01", "0.890"],
            ["67000.02", "1.456"]
        ]
    }
}
```

## 🔧 命令行参数

```bash
python api_requests/binance/binance_orderbook_requester.py [选项]

选项:
  --mode {single,continuous}  运行模式 (默认: continuous)
  --symbol SYMBOL            单次模式下的交易对符号
  --market {futures,spot}    市场类型 (默认: futures)
  --limit LIMIT              订单薄深度 (默认: 50)
```

## 📈 监控特性

- ✅ **持续运行**: 按配置间隔自动获取数据
- ✅ **批量处理**: 避免API限制，提高效率
- ✅ **错误恢复**: 自动重试失败的请求
- ✅ **详细日志**: 实时显示采集进度和统计
- ✅ **优雅停止**: 支持Ctrl+C安全停止
- ✅ **文件统计**: 显示每个币种的文件数量和大小

## 🛑 停止监控

- **Windows**: 按 `Ctrl+C`
- **命令行**: 发送 SIGINT 或 SIGTERM 信号

## 📝 日志示例

```
🚀 启动币安订单薄持续监控模式
📊 更新间隔: 30秒
📈 合约交易对: 12个
📉 现货交易对: 8个
🔄 开始第 1 轮数据采集 (2025-07-26 03:17:21)
📈 开始获取 12 个合约订单薄...
  📦 处理批次 1: ['BTCUSDT', 'ETHUSDT', 'BNBUSDT']
    ✅ BTCUSDT futures
    ✅ ETHUSDT futures
    ✅ BNBUSDT futures
📈 合约订单薄完成: 12/12
📉 开始获取 8 个现货订单薄...
✅ 第 1 轮采集完成，耗时: 25.3秒
⏰ 等待 4.7秒 后开始下一轮...
```

## ⚠️ 注意事项

1. **API限制**: 系统已内置延迟机制，避免触发币安API限制
2. **网络连接**: 确保网络连接稳定
3. **磁盘空间**: 长期运行会产生大量数据文件
4. **配置修改**: 修改配置后需要重启监控器
