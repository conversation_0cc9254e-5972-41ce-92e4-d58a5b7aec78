#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
TelegramMonitor 使用示例
展示如何使用移植后的 TelegramMonitor 模块
"""

import asyncio
import logging
from telegram_monitor import TelegramMonitor

# 配置日志
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.INFO
)
logger = logging.getLogger(__name__)

class MockSubscriptionManager:
    """模拟订阅管理器，用于测试"""
    
    def get_active_subscribers(self):
        """返回活跃订阅用户列表"""
        return []
    
    def update_alert_stats(self, user_id: int, alert_type: str):
        """更新警报统计"""
        pass

async def main():
    """主函数 - 演示如何使用 TelegramMonitor"""
    
    # 1. 创建模拟的订阅管理器
    subscription_manager = MockSubscriptionManager()
    
    # 2. 配置 Telegram 参数
    telegram_config = {
        'API_ID': 'your_api_id',           # 替换为你的 API ID
        'API_HASH': 'your_api_hash',       # 替换为你的 API Hash
        'PHONE': 'your_phone_number',      # 替换为你的手机号
        'PASSWORD': 'your_password',       # 替换为你的密码（如果有）
        'SESSION_FILE': 'telegram_session.session'  # session文件路径
    }
    
    # 3. 创建 TelegramMonitor 实例
    monitor = TelegramMonitor(subscription_manager, telegram_config)
    
    # 4. 显示当前配置
    logger.info("📊 TelegramMonitor 配置信息:")
    logger.info(f"   过滤功能启用: {monitor.FILTER_ENABLED}")
    logger.info(f"   过滤模式: {monitor.FILTER_MODE}")
    logger.info(f"   白名单频道数量: {len(monitor.ALLOWED_CHANNELS)}")
    logger.info(f"   黑名单频道数量: {len(monitor.BLOCKED_CHANNELS)}")
    
    # 5. 获取频道配置摘要
    config_summary = monitor.get_channel_config_summary()
    logger.info(f"📋 频道配置摘要: {config_summary}")
    
    # 6. 如果需要启动监听器（需要有效的 Telegram 配置）
    try:
        # 初始化客户端
        if await monitor.init_client():
            logger.info("✅ Telegram客户端初始化成功")
            
            # 启动监听器（这会一直运行直到手动停止）
            # await monitor.run_monitor()
            
        else:
            logger.error("❌ Telegram客户端初始化失败")
            
    except Exception as e:
        logger.error(f"❌ 启动监听器时出错: {e}")
    
    # 7. 显示统计信息
    stats = monitor.get_stats()
    logger.info(f"📊 运行统计: {stats}")

def configure_whitelist_example():
    """配置白名单的示例"""
    
    # 创建监听器实例
    subscription_manager = MockSubscriptionManager()
    monitor = TelegramMonitor(subscription_manager)
    
    # 示例：添加频道到白名单
    monitor.add_channel_to_allowed(123456789, "示例频道")
    
    # 示例：检查频道是否被允许
    is_allowed = monitor.is_channel_allowed(123456789, "示例频道")
    logger.info(f"频道是否被允许: {is_allowed}")

def message_processing_example():
    """消息处理的示例"""
    
    # 创建监听器实例
    subscription_manager = MockSubscriptionManager()
    monitor = TelegramMonitor(subscription_manager)
    
    # 示例消息文本
    test_message = "🚨 🚨 🚨 60,000 #ETH (145,539,453 USD) transferred from unknown wallet to Binance"
    
    # 测试黑名单过滤
    is_blacklisted = monitor._is_blacklisted(test_message, "测试频道")
    logger.info(f"消息是否被黑名单过滤: {is_blacklisted}")
    
    # 测试替换规则
    formatted_text = monitor._apply_replacement_rules(test_message, "测试频道")
    logger.info(f"格式化后的文本: {formatted_text}")
    
    # 测试重复消息检测
    is_duplicate = monitor._is_duplicate_transfer(test_message)
    logger.info(f"是否为重复消息: {is_duplicate}")

if __name__ == "__main__":
    print("🚀 TelegramMonitor 使用示例")
    print("=" * 50)
    
    # 运行配置示例
    print("\n📋 1. 配置白名单示例:")
    configure_whitelist_example()
    
    # 运行消息处理示例
    print("\n📝 2. 消息处理示例:")
    message_processing_example()
    
    # 运行主程序示例（需要有效的 Telegram 配置）
    print("\n🔧 3. 主程序示例:")
    print("注意：需要配置有效的 Telegram API 参数才能运行")
    
    # 如果要运行完整示例，取消下面的注释：
    # asyncio.run(main())
    
    print("\n✅ 示例运行完成")
    print("\n📖 使用说明:")
    print("1. 在 telegram_config 中配置你的 Telegram API 参数")
    print("2. 根据需要修改 TelegramMonitor 类的配置常量")
    print("3. 调用 asyncio.run(main()) 启动监听器")
    print("4. 监听器会自动处理接收到的消息并应用过滤规则")
