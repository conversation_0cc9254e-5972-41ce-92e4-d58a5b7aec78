#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
CoinGlass V4合约全市场数据请求器
分页获取所有交易所的合约市场综合数据（不限制交易所）
"""

import sys
import time
import requests
from pathlib import Path
from typing import Dict, Any, Optional, List

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from main import FileManager, setup_logger
from config import COINGLASS_CONFIG

class CoinglassV4FuturesAllMarketsRequester:
    """CoinGlass V4合约全市场数据请求器（所有交易所）"""
    
    def __init__(self):
        self.api_identifier = "coinglass_get_v4_futures_all_markets"
        self.logger = setup_logger(f"api_requests.{self.api_identifier}")
        self.file_manager = FileManager()
        
        # API配置
        self.base_url = COINGLASS_CONFIG["base_url"]
        self.endpoint = COINGLASS_CONFIG["endpoints"]["v4_futures_coins_markets"]
        self.api_key = COINGLASS_CONFIG["api_key"]
        self.timeout = COINGLASS_CONFIG.get("timeout", 30)
        self.max_retries = COINGLASS_CONFIG.get("max_retries", 3)
    
    def fetch_all_pages(self, per_page: int = 100) -> bool:
        """
        获取所有页面的合约市场数据（全市场，不限制交易所）
        
        Args:
            per_page: 每页数量（默认: 100, 最大: 100）
            
        Returns:
            bool: 是否成功获取数据
        """
        try:
            self.logger.info(f"🚀 开始分页获取CoinGlass V4合约全市场数据...")
            self.logger.info(f"   每页数量: {per_page}")
            self.logger.info(f"   交易所: 全市场（不限制）")
            
            all_data = []
            page = 1
            total_pages = 0
            
            # 构建请求URL
            url = f"{self.base_url}{self.endpoint}"
            
            while True:
                try:
                    # 构建请求参数（不包含exchange_list参数，获取全市场数据）
                    params = {
                        "page": page,
                        "per_page": min(per_page, 100)  # 限制最大值
                    }
                    
                    # 构建请求头
                    headers = {
                        "CG-API-KEY": self.api_key,
                        "Accept": "application/json",
                        "Content-Type": "application/json"
                    }
                    
                    # 发送请求
                    response = requests.get(url, params=params, headers=headers, timeout=self.timeout)
                    
                    if response.status_code != 200:
                        self.logger.error(f"❌ 第{page}页请求失败：HTTP {response.status_code}")
                        break
                    
                    # 解析JSON响应
                    response_data = response.json()
                    
                    # 检查响应状态
                    if response_data.get("code") != "0":
                        error_msg = response_data.get("msg", "未知错误")
                        self.logger.error(f"❌ 第{page}页API返回错误: {error_msg}")
                        break
                    
                    # 提取数据
                    page_data = response_data.get("data", [])
                    
                    if not page_data:
                        self.logger.info(f"📄 第{page}页无数据，分页获取完成")
                        break
                    
                    all_data.extend(page_data)
                    self.logger.info(f"✅ 第{page}页: 获取 {len(page_data)} 个合约币种")
                    
                    page += 1
                    total_pages = page - 1
                    
                    # 添加延迟避免API限制
                    time.sleep(0.1)
                    
                except Exception as e:
                    self.logger.error(f"❌ 第{page}页请求异常: {e}")
                    break
            
            if not all_data:
                self.logger.error("❌ 没有成功获取任何合约市场数据")
                return False
            
            # 统计各交易所的数据量
            exchange_stats = {}
            for item in all_data:
                if 'exchange' in item:
                    exchange = item['exchange']
                    exchange_stats[exchange] = exchange_stats.get(exchange, 0) + 1
            
            # 构建元数据
            metadata = {
                "request_url": url,
                "exchange_list": "全市场（不限制）",
                "per_page": per_page,
                "total_pages": total_pages,
                "symbols_count": len(all_data),
                "exchange_stats": exchange_stats,
                "data_type": "all_futures_markets_global",
                "api_response_code": "0",
                "api_response_msg": "success"
            }
            
            # 保存数据到原有存储位置
            success = self.file_manager.save_api_data(
                api_identifier=self.api_identifier,
                data=all_data,
                metadata=metadata
            )
            
            if success:
                self.logger.info(f"✅ 成功获取并保存所有合约全市场数据")
                self.logger.info(f"   总页数: {total_pages}")
                self.logger.info(f"   总币种数: {len(all_data)}")
                self.logger.info(f"   交易所统计: {exchange_stats}")
                return True
            else:
                self.logger.error("❌ 保存数据失败")
                return False
                
        except Exception as e:
            self.logger.error(f"❌ 分页获取CoinGlass V4合约全市场数据失败: {e}")
            return False
    
    def get_latest_data(self):
        """获取最新缓存的数据"""
        success, data, message = self.file_manager.load_latest_api_data(self.api_identifier)
        if success:
            self.logger.info(f"✅ 已加载最新数据: {message}")
            return data
        else:
            self.logger.error(f"❌ 加载数据失败: {message}")
            return None
    
    def is_data_fresh(self, max_age_seconds: int = 600) -> bool:
        """检查数据是否新鲜（默认10分钟）"""
        return self.file_manager.is_data_fresh(self.api_identifier, max_age_seconds)

def main():
    """主函数 - 可以直接运行此脚本"""
    requester = CoinglassV4FuturesAllMarketsRequester()
    
    # 获取全市场的合约市场数据（所有页面）
    success = requester.fetch_all_pages()
    
    if success:
        print("CoinGlass V4合约全市场数据获取成功")
        exit(0)
    else:
        print("CoinGlass V4合约全市场数据获取失败")
        exit(1)

if __name__ == "__main__":
    main()
