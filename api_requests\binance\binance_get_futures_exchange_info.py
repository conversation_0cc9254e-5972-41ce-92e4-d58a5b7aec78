#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
币安合约交易所信息API请求器
获取币安合约交易所有关的规则和交易对信息
"""

import sys
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from main import FileManager, HTTPClient, setup_logger
from config import BINANCE_CONFIG

class BinanceFuturesExchangeInfoRequester:
    """币安合约交易所信息请求器"""
    
    def __init__(self):
        """初始化请求器"""
        self.api_identifier = "binance_get_futures_exchange_info"
        self.logger = setup_logger(f"api_requests.{self.api_identifier}")
        self.file_manager = FileManager()
        
        # 创建HTTP客户端
        self.http_client = HTTPClient(
            timeout=BINANCE_CONFIG["timeout"],
            max_retries=BINANCE_CONFIG["max_retries"]
        )
        
        self.logger.info(f"✅ {self.api_identifier} 请求器初始化完成")
    
    def fetch_data(self) -> bool:
        """获取数据并存储"""
        try:
            self.logger.info("🔄 开始获取币安合约交易所信息...")
            
            # 构建请求URL
            base_url = BINANCE_CONFIG["futures_base_url"]
            endpoint = BINANCE_CONFIG["endpoints"]["futures_exchange_info"]
            url = f"{base_url}{endpoint}"
            
            # 发送请求
            success, data, message = self.http_client.get(url)
            
            if not success:
                self.logger.error(f"❌ API请求失败: {message}")
                return False
            
            # 验证响应数据
            if not isinstance(data, dict):
                self.logger.error("❌ 响应数据格式错误")
                return False
            
            # 提取关键信息作为元数据
            metadata = {
                "server_time": data.get("serverTime"),
                "timezone": data.get("timezone", "UTC"),
                "symbols_count": len(data.get("symbols", [])),
                "rate_limits_count": len(data.get("rateLimits", [])),
                "request_url": url
            }
            
            # 保存数据
            save_success, file_path, save_message = self.file_manager.save_api_data(
                api_identifier=self.api_identifier,
                data=data,
                metadata=metadata
            )
            
            if save_success:
                self.logger.info(f"✅ 数据获取并保存成功: {save_message}")
                self.logger.info(f"📊 交易对数量: {metadata['symbols_count']}")
                self.logger.info(f"⚡ 速率限制规则: {metadata['rate_limits_count']}条")
                return True
            else:
                self.logger.error(f"❌ 数据保存失败: {save_message}")
                return False
                
        except Exception as e:
            self.logger.error(f"❌ 获取数据时发生错误: {str(e)}")
            return False
        finally:
            self.http_client.close()
    
    def get_latest_data(self):
        """获取最新缓存的数据"""
        success, data, message = self.file_manager.load_latest_api_data(self.api_identifier)
        if success:
            self.logger.info(f"✅ 已加载最新数据: {message}")
            return data
        else:
            self.logger.error(f"❌ 加载数据失败: {message}")
            return None
    
    def is_data_fresh(self, max_age_seconds: int = 3600) -> bool:
        """检查数据是否新鲜（默认1小时）"""
        return self.file_manager.is_data_fresh(self.api_identifier, max_age_seconds)

def main():
    """主函数 - 可以直接运行此脚本"""
    requester = BinanceFuturesExchangeInfoRequester()
    success = requester.fetch_data()
    
    if success:
        print("币安合约交易所信息获取成功")
        exit(0)
    else:
        print("币安合约交易所信息获取失败")
        exit(1)

if __name__ == "__main__":
    main() 