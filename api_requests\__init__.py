#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
API请求器模块
包含所有外部API的独立请求器脚本
每个脚本对应一个API端点，能够独立执行
"""

# 导入所有API请求器
from .binance.binance_get_futures_exchange_info import BinanceFuturesExchangeInfoRequester
from .binance.binance_get_futures_premium_index import BinanceFuturesPremiumIndexRequester
from .binance.binance_orderbook_requester import BinanceOrderbookRequester

from .coinglass.coinglass_get_bitcoin_s2f import CoinglassBitcoinS2FRequester
from .coinglass.coinglass_get_cdri_index import CoinglassCDRIIndexRequester
from .coinglass.coinglass_get_cgdi_index import CoinglassCGDIIndexRequester
from .coinglass.coinglass_get_fear_greed_index import CoinglassFearGreedIndexRequester
from .coinglass.coinglass_get_golden_ratio import CoinglassGoldenRatioRequester
from .coinglass.coinglass_get_rsi_list import CoinglassRSIListRequester
from .coinglass.coinglass_get_v4_futures_all_markets import CoinglassV4FuturesAllMarketsRequester
from .coinglass.coinglass_get_v4_futures_binance_markets import CoinglassV4FuturesBinanceMarketsRequester
from .coinglass.coinglass_get_v4_spot_all_markets import CoinglassV4SpotAllMarketsRequester

from .google_ai.google_ai_generate_gemini_content import GoogleAIGeminiContentRequester

__all__ = [
    # Binance API请求器
    'BinanceFuturesExchangeInfoRequester',
    'BinanceFuturesPremiumIndexRequester',
    'BinanceOrderbookRequester',

    # CoinGlass API请求器
    'CoinglassBitcoinS2FRequester',
    'CoinglassCDRIIndexRequester',
    'CoinglassCGDIIndexRequester',
    'CoinglassFearGreedIndexRequester',
    'CoinglassGoldenRatioRequester',
    'CoinglassRSIListRequester',
    'CoinglassV4FuturesAllMarketsRequester',
    'CoinglassV4FuturesBinanceMarketsRequester',
    'CoinglassV4SpotAllMarketsRequester',

    # Google AI API请求器
    'GoogleAIGeminiContentRequester'
]