#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版管理员数据报告生成器
"""

import json
import os
import sys
from datetime import datetime, timedelta
from pathlib import Path

# 配置文件路径
USER_DATA_FILE = "data/user_data.json"
ORDERS_FILE = "data/orders.json"
BALANCE_HISTORY_FILE = "data/balance_history.json"
INVITATION_DATA_FILE = "data/invitation_data.json"

def load_json_safe(filename, default=None):
    """安全加载JSON文件"""
    try:
        if os.path.exists(filename):
            with open(filename, 'r', encoding='utf-8') as f:
                return json.load(f)
        return default or {}
    except Exception as e:
        print(f"加载文件失败 {filename}: {e}")
        return default or {}

def generate_report():
    """生成简化版数据报告"""
    try:
        print("📊 开始生成数据分析报告...")
        
        # 加载数据
        user_data = load_json_safe(USER_DATA_FILE, {})
        orders_data = load_json_safe(ORDERS_FILE, {})
        balance_history = load_json_safe(BALANCE_HISTORY_FILE, {})
        invitation_data = load_json_safe(INVITATION_DATA_FILE, {})
        
        # 生成报告内容
        report_lines = []
        report_lines.append("=" * 60)
        report_lines.append("📊 钱大师机器人数据分析报告")
        report_lines.append("=" * 60)
        report_lines.append(f"📅 生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        report_lines.append("")
        
        # 用户统计
        report_lines.append("👥 用户统计")
        report_lines.append("-" * 30)
        total_users = len(user_data)
        active_users = sum(1 for user in user_data.values() if user.get('points', 0) > 0)
        report_lines.append(f"总用户数: {total_users}")
        report_lines.append(f"活跃用户数: {active_users}")
        report_lines.append(f"活跃率: {(active_users/total_users*100):.1f}%" if total_users > 0 else "活跃率: 0%")
        report_lines.append("")
        
        # 积分统计
        report_lines.append("💎 积分统计")
        report_lines.append("-" * 30)
        total_points = sum(user.get('points', 0) for user in user_data.values())
        avg_points = total_points / total_users if total_users > 0 else 0
        report_lines.append(f"总积分: {total_points}")
        report_lines.append(f"平均积分: {avg_points:.1f}")
        report_lines.append("")
        
        # 充值统计
        report_lines.append("💰 充值统计")
        report_lines.append("-" * 30)
        total_recharged = sum(user.get('total_recharged', 0) for user in user_data.values())
        paying_users = sum(1 for user in user_data.values() if user.get('total_recharged', 0) > 0)
        report_lines.append(f"总充值金额: {total_recharged:.2f} USDT")
        report_lines.append(f"付费用户数: {paying_users}")
        report_lines.append(f"付费率: {(paying_users/total_users*100):.1f}%" if total_users > 0 else "付费率: 0%")
        report_lines.append("")
        
        # 订单统计
        report_lines.append("📋 订单统计")
        report_lines.append("-" * 30)
        total_orders = len(orders_data)
        completed_orders = sum(1 for order in orders_data.values() if order.get('status') == 'completed')
        pending_orders = sum(1 for order in orders_data.values() if order.get('status') == 'pending')
        report_lines.append(f"总订单数: {total_orders}")
        report_lines.append(f"已完成订单: {completed_orders}")
        report_lines.append(f"待处理订单: {pending_orders}")
        report_lines.append("")
        
        # 邀请统计
        report_lines.append("👥 邀请统计")
        report_lines.append("-" * 30)
        invitations = invitation_data.get('invitations', {})
        relationships = invitation_data.get('relationships', {})
        total_inviters = len(invitations)
        total_invitees = len(relationships)
        total_commissions = invitation_data.get('stats', {}).get('total_commissions', 0)
        report_lines.append(f"邀请者数量: {total_inviters}")
        report_lines.append(f"被邀请者数量: {total_invitees}")
        report_lines.append(f"总佣金: {total_commissions:.2f} USDT")
        report_lines.append("")
        
        # 系统状态
        report_lines.append("🔧 系统状态")
        report_lines.append("-" * 30)
        data_files = [USER_DATA_FILE, ORDERS_FILE, BALANCE_HISTORY_FILE, INVITATION_DATA_FILE]
        for file_path in data_files:
            if os.path.exists(file_path):
                size = os.path.getsize(file_path)
                report_lines.append(f"{os.path.basename(file_path)}: {size} bytes")
            else:
                report_lines.append(f"{os.path.basename(file_path)}: 文件不存在")
        report_lines.append("")
        
        # 生成报告文件
        timestamp = datetime.now().strftime('%Y-%m-%d_%H-%M-%S')
        report_filename = f"admin_data_report_{timestamp}.txt"
        
        with open(report_filename, 'w', encoding='utf-8') as f:
            f.write('\n'.join(report_lines))
        
        print(f"✅ 报告生成完成: {report_filename}")
        print(f"📄 报告行数: {len(report_lines)}")
        
        return True
        
    except Exception as e:
        print(f"❌ 生成报告失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    try:
        success = generate_report()
        sys.exit(0 if success else 1)
    except Exception as e:
        print(f"❌ 脚本执行失败: {e}")
        sys.exit(1)
