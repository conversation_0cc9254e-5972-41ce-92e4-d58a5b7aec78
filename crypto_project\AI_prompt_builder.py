#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI币种查询功能 - AI提示词构建器
将各种数据格式化为结构化的AI分析提示词
"""

import json
import os
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
import logging

logger = logging.getLogger(__name__)

# 导入AI订单薄管理器
try:
    from AI_orderbook_manager import AIOrderbookManager
except ImportError:
    logger.warning("AI_orderbook_manager 导入失败，订单薄数据功能将不可用")
    AIOrderbookManager = None

# 导入智能格式化函数和时间函数
# 使用延迟导入避免循环导入
def _get_formatting_functions():
    """延迟导入格式化函数"""
    try:
        import importlib
        crypto_bot_module = importlib.import_module('crypto_trading_bot')
        return {
            'smart_price_format': getattr(crypto_bot_module, 'smart_price_format', None),
            'smart_percentage_format': getattr(crypto_bot_module, 'smart_percentage_format', None),
            'smart_volume_format': getattr(crypto_bot_module, 'smart_volume_format', None),
            'format_beijing_time': getattr(crypto_bot_module, 'format_beijing_time', None),
            'get_beijing_time': getattr(crypto_bot_module, 'get_beijing_time', None)
        }
    except Exception:
        return {}

# 尝试获取主模块的函数
_imported_funcs = _get_formatting_functions()

# 如果导入失败，定义本地版本
if not _imported_funcs.get('smart_price_format'):
    # 如果导入失败，定义本地版本
    def smart_price_format(price: float) -> str:
        """智能价格格式化函数 - 基于数据的最大精度，自动去除尾随零"""
        try:
            price_float = float(price)
            if price_float == 0:
                return "$0.00"
            
            # 将价格转为字符串以检测原始精度
            price_str = str(price_float)
            
            # 如果是科学计数法，转换为标准格式
            if 'e' in price_str.lower():
                # 对于科学计数法，使用足够的精度
                if price_float < 1e-6:
                    decimal_places = 10
                elif price_float < 1e-4:
                    decimal_places = 8
                elif price_float < 1e-2:
                    decimal_places = 6
                else:
                    decimal_places = 4
            else:
                # 检测小数部分的精度
                if '.' in price_str:
                    decimal_part = price_str.split('.')[1].rstrip('0')
                    decimal_places = len(decimal_part)
                    # 确保至少有2位小数，最多10位
                    decimal_places = max(2, min(decimal_places, 10))
                else:
                    decimal_places = 2
            
            # 生成格式化字符串
            if price_float >= 1000:
                # 大额数字添加千分位分隔符
                if decimal_places <= 2:
                    formatted = f"${price_float:,.{decimal_places}f}"
                else:
                    # 大额数字但有高精度小数的情况
                    formatted = f"${price_float:,.{min(4, decimal_places)}f}"
            else:
                # 小额数字保持完整精度
                formatted = f"${price_float:.{decimal_places}f}"
            
            # 去除尾随的零，但保留至少2位小数
            if '.' in formatted:
                # 分离美元符号和数字部分
                dollar_sign = formatted[0]
                number_part = formatted[1:]
                
                # 去除尾随零，但保留至少2位小数
                if ',' in number_part:
                    # 处理带千分位分隔符的情况
                    parts = number_part.split('.')
                    if len(parts) == 2:
                        integer_part = parts[0]
                        decimal_part = parts[1].rstrip('0')
                        # 确保至少2位小数
                        if len(decimal_part) < 2:
                            decimal_part = decimal_part.ljust(2, '0')
                        number_part = f"{integer_part}.{decimal_part}"
                else:
                    # 处理普通数字
                    parts = number_part.split('.')
                    if len(parts) == 2:
                        integer_part = parts[0]
                        decimal_part = parts[1].rstrip('0')
                        # 确保至少2位小数
                        if len(decimal_part) < 2:
                            decimal_part = decimal_part.ljust(2, '0')
                        number_part = f"{integer_part}.{decimal_part}"
                
                formatted = dollar_sign + number_part
            
            return formatted
                
        except Exception as e:
            return f"${price}"
    
    def smart_percentage_format(percentage: float) -> str:
        """智能百分比格式化 - 基于数据精度"""
        try:
            percentage_float = float(percentage)
            
            # 检测原始精度
            percentage_str = str(abs(percentage_float))
            if '.' in percentage_str:
                decimal_part = percentage_str.split('.')[1].rstrip('0')
                decimal_places = len(decimal_part)
                decimal_places = max(2, min(decimal_places, 6))  # 2-6位小数
            else:
                decimal_places = 2
            
            return f"{percentage_float:+.{decimal_places}f}%"
        except:
            return f"{percentage}%"
    
    def smart_volume_format(volume: float) -> str:
        """智能成交量格式化 - 保持原始精度"""
        try:
            volume_float = float(volume)
            
            # 检测原始精度
            volume_str = str(volume_float)
            if '.' in volume_str:
                decimal_part = volume_str.split('.')[1].rstrip('0')
                decimal_places = len(decimal_part)
                decimal_places = min(decimal_places, 4)  # 最多4位小数
            else:
                decimal_places = 0
            
            if volume_float >= 1e12:
                scaled = volume_float / 1e12
                return f"${scaled:.{min(2, decimal_places)}f}T"
            elif volume_float >= 1e9:
                scaled = volume_float / 1e9
                return f"${scaled:.{min(2, decimal_places)}f}B"
            elif volume_float >= 1e6:
                scaled = volume_float / 1e6
                return f"${scaled:.{min(2, decimal_places)}f}M"
            elif volume_float >= 1e3:
                scaled = volume_float / 1e3
                return f"${scaled:.{min(2, decimal_places)}f}K"
            else:
                if decimal_places > 0:
                    return f"${volume_float:.{decimal_places}f}"
                else:
                    return f"${volume_float:,.0f}"
        except:
            return f"${volume}"

    # 时间函数备用定义
    def format_beijing_time(dt_str, format_str="%Y-%m-%d %H:%M:%S"):
        """备用时间格式化函数"""
        return datetime.now().strftime(format_str)
    
    def get_beijing_time():
        """备用北京时间获取函数"""
        from datetime import timezone, timedelta
        beijing_tz = timezone(timedelta(hours=8))
        return datetime.now(beijing_tz)

class AIPromptBuilder:
    """AI提示词构建器 - 将数据格式化为专业的分析提示词"""
    
    def __init__(self):
        """初始化提示词构建器"""
        self.base_prompt_template = None
        # 初始化订单薄管理器
        if AIOrderbookManager:
            self.orderbook_manager = AIOrderbookManager()
            logger.info("✅ AI订单薄管理器初始化完成")
        else:
            self.orderbook_manager = None
            logger.warning("⚠️ AI订单薄管理器不可用")
        logger.info("✅ AI提示词构建器初始化完成")
    
    def build_comprehensive_analysis_prompt(self, 
                                          symbol: str,
                                          market_type: str,
                                          interval: str,
                                          kline_data: pd.DataFrame,
                                          technical_indicators: Dict[str, Any],
                                          market_data: Dict[str, Any],
                                          trading_signals: Dict[str, str]) -> str:
        """构建综合分析提示词
        
        Args:
            symbol: 币种符号
            market_type: 市场类型 (futures/spot)
            interval: K线周期
            kline_data: K线数据
            technical_indicators: 技术指标
            market_data: 市场综合数据
            trading_signals: 交易信号
            
        Returns:
            格式化的AI分析提示词
        """
        try:
            # 构建基础信息
            current_time = format_beijing_time(get_beijing_time().isoformat(), "%Y-%m-%d %H:%M:%S")
            
            # 提取关键市场数据
            market_summary = self._extract_market_summary(symbol, market_data)
            
            # 格式化K线数据摘要
            kline_summary = self._format_kline_summary(kline_data, interval)
            
            # 格式化技术指标
            technical_summary = self._format_technical_indicators(technical_indicators)
            
            # 格式化交易信号
            signal_summary = self._format_trading_signals(trading_signals)
            
            # 构建专业模板提示词
            prompt = self._build_professional_template_prompt(
                symbol, market_type, interval, current_time,
                kline_data, technical_indicators, market_data, trading_signals
            )

            logger.info(f"✅ 为 {symbol} 构建了综合分析提示词，长度: {len(prompt)} 字符")
            return prompt
            
        except Exception as e:
            logger.error(f"❌ 构建提示词失败: {str(e)}")
            return self._get_fallback_prompt(symbol, market_type)
    
    def _build_professional_template_prompt(self, symbol: str, market_type: str, 
                                          interval: str, current_time: str,
                                          kline_data: pd.DataFrame, 
                                          technical_indicators: Dict[str, Any],
                                          market_data: Dict[str, Any], 
                                          trading_signals: Dict[str, str]) -> str:
        """构建基于专业模板的分析提示词"""
        try:
            # 提取币种名称
            coin_name = symbol.replace('USDT', '').upper()
            
            # 提取关键价格数据
            current_price = kline_data['close'].iloc[-1]
            high_24h = kline_data['high'].tail(24).max() if len(kline_data) >= 24 else kline_data['high'].max()
            low_24h = kline_data['low'].tail(24).min() if len(kline_data) >= 24 else kline_data['low'].min()
            
            # 计算价格变化
            prev_price = kline_data['close'].iloc[-2] if len(kline_data) >= 2 else current_price
            price_change_percent = ((current_price - prev_price) / prev_price * 100) if prev_price != 0 else 0
            change_direction = "🟢" if price_change_percent > 0 else "🔴" if price_change_percent < 0 else "⚪"
            arrow = "↑" if price_change_percent > 0 else "↓" if price_change_percent < 0 else "→"
            
            # 成交量数据
            current_volume = kline_data['volume_usd'].iloc[-1] if 'volume_usd' in kline_data.columns else 0
            avg_volume = kline_data['volume_usd'].mean() if 'volume_usd' in kline_data.columns else current_volume
            volume_change = ((current_volume - avg_volume) / avg_volume * 100) if avg_volume != 0 else 0
            
            # 技术指标简化
            rsi = technical_indicators.get('rsi', 50)
            adx = technical_indicators.get('adx', 25)
            ema_20 = technical_indicators.get('ema_20', current_price)
            sma_50 = technical_indicators.get('sma_50', current_price)
            bb_upper = technical_indicators.get('bb_upper', current_price * 1.02)
            bb_lower = technical_indicators.get('bb_lower', current_price * 0.98)
            
            # 多周期技术指标摘要
            multi_period_data = self._format_multi_period_indicators(technical_indicators)
            
            # CoinGlass数据格式化
            coinglass_data = self._format_coinglass_data(technical_indicators, symbol)
            
            # 趋势强度描述
            if adx > 50:
                trend_strength = "极强"
            elif adx > 25:
                trend_strength = "强"
            else:
                trend_strength = "弱"
            
            # 提取市场数据
            market_summary = self._extract_market_summary(symbol, market_data)
            
            # 构建专业模板提示词
            prompt = f"""角色：你是一名世界最强顶级的加密货币市场分析AI，专注于数据驱动市场分析。你的任务是为用户生成一份简洁、清晰、包含关键数据指标的分析报告。

格式要求：
- 禁止使用加粗语法：**
- 严格遵循提供的Markdown模板结构
- 所有数据占位符必须被真实数据填充
- 价格精度要求：必须按照输入数据的最大精度输出，不得降低精度
- 对于小价值币种（如GUSDT），价格必须显示足够的小数位以体现真实价格变化
- 对于上升/积极指标使用🟢和向上箭头↑
- 对于下降/消极指标使用🔴和向下箭头↓
- 语言风格：专业、客观、数据导向
- 不要在报告中添加免责声明，不要ai署名
在报告的所有部分，特别是关于关键价位（买入/卖出参考、止损位置、阻力位、支撑位，技术指标等）以及交易策略建议中的价格区间和目标位时，请严格遵循以下原则：

1.  仅提供数值： 只给出具体的数字或价格，例如 `$19.351`。
2.  禁止附加说明： 绝对不要在数值后面添加任何括号内的解释性文字、判断逻辑或描述性短语。

请根据以下数据生成{coin_name}的专业分析报告：

【基础市场数据】
- 资产名称：{coin_name}
- 交易对：{symbol}
- 市场类型：{"合约" if market_type == "futures" else "合约"}
- 分析周期：{interval}
- 分析时间：{current_time}
- 当前价格：{smart_price_format(current_price)}
- 24小时最高价：{smart_price_format(high_24h)}
- 24小时最低价：{smart_price_format(low_24h)}
- 24小时涨跌幅：{change_direction} {arrow} {smart_percentage_format(price_change_percent)}
- 当前成交量：{smart_volume_format(current_volume)}
- 成交量变化：{smart_percentage_format(volume_change)}

【现货全市场数据】

【现货基础价格数据】
- 现货当前价格：{smart_price_format(technical_indicators.get('coinglass_spot_current_price', 0))}
- 现货市值：{smart_volume_format(technical_indicators.get('coinglass_spot_market_cap', 0))}

【现货价格变化数据 - 8个时间周期】
- 5分钟价格变化：{smart_price_format(technical_indicators.get('coinglass_spot_price_change_5m', 0))} ({smart_percentage_format(technical_indicators.get('coinglass_spot_price_change_percent_5m', 0))})
- 15分钟价格变化：{smart_price_format(technical_indicators.get('coinglass_spot_price_change_15m', 0))} ({smart_percentage_format(technical_indicators.get('coinglass_spot_price_change_percent_15m', 0))})
- 30分钟价格变化：{smart_price_format(technical_indicators.get('coinglass_spot_price_change_30m', 0))} ({smart_percentage_format(technical_indicators.get('coinglass_spot_price_change_percent_30m', 0))})
- 1小时价格变化：{smart_price_format(technical_indicators.get('coinglass_spot_price_change_1h', 0))} ({smart_percentage_format(technical_indicators.get('coinglass_spot_price_change_percent_1h', 0))})
- 4小时价格变化：{smart_price_format(technical_indicators.get('coinglass_spot_price_change_4h', 0))} ({smart_percentage_format(technical_indicators.get('coinglass_spot_price_change_percent_4h', 0))})
- 12小时价格变化：{smart_price_format(technical_indicators.get('coinglass_spot_price_change_12h', 0))} ({smart_percentage_format(technical_indicators.get('coinglass_spot_price_change_percent_12h', 0))})
- 24小时价格变化：{smart_price_format(technical_indicators.get('coinglass_spot_price_change_24h', 0))} ({smart_percentage_format(technical_indicators.get('coinglass_spot_price_change_percent_24h', 0))})
- 1周价格变化：{smart_price_format(technical_indicators.get('coinglass_spot_price_change_1w', 0))} ({smart_percentage_format(technical_indicators.get('coinglass_spot_price_change_percent_1w', 0))})

【现货成交量数据 - 8个时间周期】
- 5分钟成交量：{smart_volume_format(technical_indicators.get('coinglass_spot_volume_5m', 0))}
- 15分钟成交量：{smart_volume_format(technical_indicators.get('coinglass_spot_volume_15m', 0))}
- 30分钟成交量：{smart_volume_format(technical_indicators.get('coinglass_spot_volume_30m', 0))}
- 1小时成交量：{smart_volume_format(technical_indicators.get('coinglass_spot_volume_1h', 0))}
- 4小时成交量：{smart_volume_format(technical_indicators.get('coinglass_spot_volume_4h', 0))}
- 12小时成交量：{smart_volume_format(technical_indicators.get('coinglass_spot_volume_12h', 0))}
- 24小时成交量：{smart_volume_format(technical_indicators.get('coinglass_spot_volume_24h', 0))}
- 1周成交量：{smart_volume_format(technical_indicators.get('coinglass_spot_volume_1w', 0))}

【现货成交量变化数据 - 8个时间周期】
- 5分钟成交量变化：{smart_volume_format(technical_indicators.get('coinglass_spot_volume_change_5m', 0))} ({smart_percentage_format(technical_indicators.get('coinglass_spot_volume_change_percent_5m', 0))})
- 15分钟成交量变化：{smart_volume_format(technical_indicators.get('coinglass_spot_volume_change_15m', 0))} ({smart_percentage_format(technical_indicators.get('coinglass_spot_volume_change_percent_15m', 0))})
- 30分钟成交量变化：{smart_volume_format(technical_indicators.get('coinglass_spot_volume_change_30m', 0))} ({smart_percentage_format(technical_indicators.get('coinglass_spot_volume_change_percent_30m', 0))})
- 1小时成交量变化：{smart_volume_format(technical_indicators.get('coinglass_spot_volume_change_1h', 0))} ({smart_percentage_format(technical_indicators.get('coinglass_spot_volume_change_percent_1h', 0))})
- 4小时成交量变化：{smart_volume_format(technical_indicators.get('coinglass_spot_volume_change_4h', 0))} ({smart_percentage_format(technical_indicators.get('coinglass_spot_volume_change_percent_4h', 0))})
- 12小时成交量变化：{smart_volume_format(technical_indicators.get('coinglass_spot_volume_change_12h', 0))} ({smart_percentage_format(technical_indicators.get('coinglass_spot_volume_change_percent_12h', 0))})
- 24小时成交量变化：{smart_volume_format(technical_indicators.get('coinglass_spot_volume_change_24h', 0))} ({smart_percentage_format(technical_indicators.get('coinglass_spot_volume_change_percent_24h', 0))})
- 1周成交量变化：{smart_volume_format(technical_indicators.get('coinglass_spot_volume_change_1w', 0))} ({smart_percentage_format(technical_indicators.get('coinglass_spot_volume_change_percent_1w', 0))})

【现货买卖量数据 - 8个时间周期】
- 5分钟买入量：{smart_volume_format(technical_indicators.get('coinglass_spot_buy_volume_5m', 0))} | 卖出量：{smart_volume_format(technical_indicators.get('coinglass_spot_sell_volume_5m', 0))}
- 15分钟买入量：{smart_volume_format(technical_indicators.get('coinglass_spot_buy_volume_15m', 0))} | 卖出量：{smart_volume_format(technical_indicators.get('coinglass_spot_sell_volume_15m', 0))}
- 30分钟买入量：{smart_volume_format(technical_indicators.get('coinglass_spot_buy_volume_30m', 0))} | 卖出量：{smart_volume_format(technical_indicators.get('coinglass_spot_sell_volume_30m', 0))}
- 1小时买入量：{smart_volume_format(technical_indicators.get('coinglass_spot_buy_volume_1h', 0))} | 卖出量：{smart_volume_format(technical_indicators.get('coinglass_spot_sell_volume_1h', 0))}
- 4小时买入量：{smart_volume_format(technical_indicators.get('coinglass_spot_buy_volume_4h', 0))} | 卖出量：{smart_volume_format(technical_indicators.get('coinglass_spot_sell_volume_4h', 0))}
- 12小时买入量：{smart_volume_format(technical_indicators.get('coinglass_spot_buy_volume_12h', 0))} | 卖出量：{smart_volume_format(technical_indicators.get('coinglass_spot_sell_volume_12h', 0))}
- 24小时买入量：{smart_volume_format(technical_indicators.get('coinglass_spot_buy_volume_24h', 0))} | 卖出量：{smart_volume_format(technical_indicators.get('coinglass_spot_sell_volume_24h', 0))}
- 1周买入量：{smart_volume_format(technical_indicators.get('coinglass_spot_buy_volume_1w', 0))} | 卖出量：{smart_volume_format(technical_indicators.get('coinglass_spot_sell_volume_1w', 0))}

【现货资金流向数据 - 8个时间周期】
- 5分钟资金流向：{smart_volume_format(technical_indicators.get('coinglass_spot_volume_flow_5m', 0))}
- 15分钟资金流向：{smart_volume_format(technical_indicators.get('coinglass_spot_volume_flow_15m', 0))}
- 30分钟资金流向：{smart_volume_format(technical_indicators.get('coinglass_spot_volume_flow_30m', 0))}
- 1小时资金流向：{smart_volume_format(technical_indicators.get('coinglass_spot_volume_flow_1h', 0))}
- 4小时资金流向：{smart_volume_format(technical_indicators.get('coinglass_spot_volume_flow_4h', 0))}
- 12小时资金流向：{smart_volume_format(technical_indicators.get('coinglass_spot_volume_flow_12h', 0))}
- 24小时资金流向：{smart_volume_format(technical_indicators.get('coinglass_spot_volume_flow_24h', 0))}
- 1周资金流向：{smart_volume_format(technical_indicators.get('coinglass_spot_volume_flow_1w', 0))}

【币安期权市场数据】
- 期权持仓量：{smart_volume_format(technical_indicators.get('coinglass_option_oi', 0))}
- 期权成交量：{smart_volume_format(technical_indicators.get('coinglass_option_volume', 0))}
- 持仓量24h变化：{smart_percentage_format(technical_indicators.get('coinglass_option_oi_change', 0))}
- 成交量24h变化：{smart_percentage_format(technical_indicators.get('coinglass_option_volume_change', 0))}
- 期权净资金流向：{smart_volume_format(technical_indicators.get('coinglass_option_net_flow', 0))}
- 期权流向比率：{technical_indicators.get('coinglass_option_flow_ratio', 1.0):.4f}
- 期权/现货比率：{smart_percentage_format(technical_indicators.get('coinglass_option_spot_ratio', 0))}

【币安合约市场完整数据】

【合约基础数据】
- 合约当前价格：{smart_price_format(technical_indicators.get('coinglass_futures_current_price', 0))}
- 合约市值比率：{smart_percentage_format(technical_indicators.get('coinglass_futures_market_cap_ratio', 0))}
- 持仓量：{smart_volume_format(technical_indicators.get('coinglass_futures_oi_usd', 0))}
- 持仓数量：{technical_indicators.get('coinglass_futures_oi_quantity', 0):.3f}
- 持仓成交量比：{technical_indicators.get('coinglass_futures_oi_volume_ratio', 0):.4f}

【合约资金费率数据】
- 按持仓量加权资金费率：{smart_percentage_format(technical_indicators.get('coinglass_futures_avg_funding_rate_by_oi', 0))}
- 按成交量加权资金费率：{smart_percentage_format(technical_indicators.get('coinglass_futures_avg_funding_rate_by_vol', 0))}

【合约持仓成交比变化数据】
- 1小时OI/Vol比率变化：{smart_percentage_format(technical_indicators.get('coinglass_futures_oi_vol_ratio_change_1h', 0))}
- 4小时OI/Vol比率变化：{smart_percentage_format(technical_indicators.get('coinglass_futures_oi_vol_ratio_change_4h', 0))}
- 24小时OI/Vol比率变化：{smart_percentage_format(technical_indicators.get('coinglass_futures_oi_vol_ratio_change_24h', 0))}

【合约价格变化数据 - 8个时间周期】
- 5分钟价格变化：{smart_percentage_format(technical_indicators.get('coinglass_futures_price_change_percent_5m', 0))}
- 15分钟价格变化：{smart_percentage_format(technical_indicators.get('coinglass_futures_price_change_percent_15m', 0))}
- 30分钟价格变化：{smart_percentage_format(technical_indicators.get('coinglass_futures_price_change_percent_30m', 0))}
- 1小时价格变化：{smart_percentage_format(technical_indicators.get('coinglass_futures_price_change_percent_1h', 0))}
- 4小时价格变化：{smart_percentage_format(technical_indicators.get('coinglass_futures_price_change_percent_4h', 0))}
- 12小时价格变化：{smart_percentage_format(technical_indicators.get('coinglass_futures_price_change_percent_12h', 0))}
- 24小时价格变化：{smart_percentage_format(technical_indicators.get('coinglass_futures_price_change_percent_24h', 0))}

【合约持仓量变化数据 - 6个时间周期】
- 5分钟持仓量变化：{smart_percentage_format(technical_indicators.get('coinglass_futures_oi_change_percent_5m', 0))} ({smart_volume_format(technical_indicators.get('coinglass_futures_oi_change_usd_5m', 0))})
- 15分钟持仓量变化：{smart_percentage_format(technical_indicators.get('coinglass_futures_oi_change_percent_15m', 0))} ({smart_volume_format(technical_indicators.get('coinglass_futures_oi_change_usd_15m', 0))})
- 30分钟持仓量变化：{smart_percentage_format(technical_indicators.get('coinglass_futures_oi_change_percent_30m', 0))} ({smart_volume_format(technical_indicators.get('coinglass_futures_oi_change_usd_30m', 0))})
- 1小时持仓量变化：{smart_percentage_format(technical_indicators.get('coinglass_futures_oi_change_percent_1h', 0))} ({smart_volume_format(technical_indicators.get('coinglass_futures_oi_change_usd_1h', 0))})
- 4小时持仓量变化：{smart_percentage_format(technical_indicators.get('coinglass_futures_oi_change_percent_4h', 0))} ({smart_volume_format(technical_indicators.get('coinglass_futures_oi_change_usd_4h', 0))})
- 24小时持仓量变化：{smart_percentage_format(technical_indicators.get('coinglass_futures_oi_change_percent_24h', 0))} ({smart_volume_format(technical_indicators.get('coinglass_futures_oi_change_usd_24h', 0))})

【合约成交量变化数据 - 3个时间周期】
- 1小时成交量变化：{smart_percentage_format(technical_indicators.get('coinglass_futures_volume_change_percent_1h', 0))} ({smart_volume_format(technical_indicators.get('coinglass_futures_volume_change_usd_1h', 0))})
- 4小时成交量变化：{smart_percentage_format(technical_indicators.get('coinglass_futures_volume_change_percent_4h', 0))} ({smart_volume_format(technical_indicators.get('coinglass_futures_volume_change_usd_4h', 0))})
- 24小时成交量变化：{smart_percentage_format(technical_indicators.get('coinglass_futures_volume_change_percent_24h', 0))} ({smart_volume_format(technical_indicators.get('coinglass_futures_volume_change_usd_24h', 0))})

【合约多空比数据 - 7个时间周期】
- 5分钟多空比：{technical_indicators.get('coinglass_futures_long_short_ratio_5m', 1.0):.4f}
- 15分钟多空比：{technical_indicators.get('coinglass_futures_long_short_ratio_15m', 1.0):.4f}
- 30分钟多空比：{technical_indicators.get('coinglass_futures_long_short_ratio_30m', 1.0):.4f}
- 1小时多空比：{technical_indicators.get('coinglass_futures_long_short_ratio_1h', 1.0):.4f}
- 4小时多空比：{technical_indicators.get('coinglass_futures_long_short_ratio_4h', 1.0):.4f}
- 12小时多空比：{technical_indicators.get('coinglass_futures_long_short_ratio_12h', 1.0):.4f}
- 24小时多空比：{technical_indicators.get('coinglass_futures_long_short_ratio_24h', 1.0):.4f}

【合约多空成交量数据 - 7个时间周期】
- 5分钟多头成交：{smart_volume_format(technical_indicators.get('coinglass_futures_long_volume_5m', 0))} | 空头成交：{smart_volume_format(technical_indicators.get('coinglass_futures_short_volume_5m', 0))}
- 15分钟多头成交：{smart_volume_format(technical_indicators.get('coinglass_futures_long_volume_15m', 0))} | 空头成交：{smart_volume_format(technical_indicators.get('coinglass_futures_short_volume_15m', 0))}
- 30分钟多头成交：{smart_volume_format(technical_indicators.get('coinglass_futures_long_volume_30m', 0))} | 空头成交：{smart_volume_format(technical_indicators.get('coinglass_futures_short_volume_30m', 0))}
- 1小时多头成交：{smart_volume_format(technical_indicators.get('coinglass_futures_long_volume_1h', 0))} | 空头成交：{smart_volume_format(technical_indicators.get('coinglass_futures_short_volume_1h', 0))}
- 4小时多头成交：{smart_volume_format(technical_indicators.get('coinglass_futures_long_volume_4h', 0))} | 空头成交：{smart_volume_format(technical_indicators.get('coinglass_futures_short_volume_4h', 0))}
- 12小时多头成交：{smart_volume_format(technical_indicators.get('coinglass_futures_long_volume_12h', 0))} | 空头成交：{smart_volume_format(technical_indicators.get('coinglass_futures_short_volume_12h', 0))}
- 24小时多头成交：{smart_volume_format(technical_indicators.get('coinglass_futures_long_volume_24h', 0))} | 空头成交：{smart_volume_format(technical_indicators.get('coinglass_futures_short_volume_24h', 0))}

【合约爆仓数据 - 4个时间周期】
- 1小时爆仓总额：{smart_volume_format(technical_indicators.get('coinglass_futures_liquidation_1h', 0))} | 多头爆仓：{smart_volume_format(technical_indicators.get('coinglass_futures_long_liquidation_1h', 0))} | 空头爆仓：{smart_volume_format(technical_indicators.get('coinglass_futures_short_liquidation_1h', 0))}
- 4小时爆仓总额：{smart_volume_format(technical_indicators.get('coinglass_futures_liquidation_4h', 0))} | 多头爆仓：{smart_volume_format(technical_indicators.get('coinglass_futures_long_liquidation_4h', 0))} | 空头爆仓：{smart_volume_format(technical_indicators.get('coinglass_futures_short_liquidation_4h', 0))}
- 12小时爆仓总额：{smart_volume_format(technical_indicators.get('coinglass_futures_liquidation_12h', 0))} | 多头爆仓：{smart_volume_format(technical_indicators.get('coinglass_futures_long_liquidation_12h', 0))} | 空头爆仓：{smart_volume_format(technical_indicators.get('coinglass_futures_short_liquidation_12h', 0))}
- 24小时爆仓总额：{smart_volume_format(technical_indicators.get('coinglass_futures_liquidation_24h', 0))} | 多头爆仓：{smart_volume_format(technical_indicators.get('coinglass_futures_long_liquidation_24h', 0))} | 空头爆仓：{smart_volume_format(technical_indicators.get('coinglass_futures_short_liquidation_24h', 0))}

【CoinGlass订单薄数据】

【大额订单分析】
- 当前大额订单总数：{technical_indicators.get('orderbook_large_orders_count', 0)}个
- 大额买单数量：{technical_indicators.get('orderbook_buy_orders_count', 0)}个 | 大额卖单数量：{technical_indicators.get('orderbook_sell_orders_count', 0)}个
- 大额买单总价值：{smart_volume_format(technical_indicators.get('orderbook_total_buy_value', 0))}
- 大额卖单总价值：{smart_volume_format(technical_indicators.get('orderbook_total_sell_value', 0))}
- 最大买单：{smart_volume_format(technical_indicators.get('orderbook_largest_buy_order', 0))}
- 最大卖单：{smart_volume_format(technical_indicators.get('orderbook_largest_sell_order', 0))}
- 平均买单价格：{smart_price_format(technical_indicators.get('orderbook_avg_buy_price', 0))}
- 平均卖单价格：{smart_price_format(technical_indicators.get('orderbook_avg_sell_price', 0))}

【订单薄深度数据】
- 买盘深度金额：{smart_volume_format(technical_indicators.get('orderbook_latest_bids_usd', 0))}
- 卖盘深度金额：{smart_volume_format(technical_indicators.get('orderbook_latest_asks_usd', 0))}
- 买盘深度数量：{technical_indicators.get('orderbook_latest_bids_quantity', 0):.3f}
- 卖盘深度数量：{technical_indicators.get('orderbook_latest_asks_quantity', 0):.3f}
- 买卖比率：{technical_indicators.get('orderbook_bid_ask_ratio', 0):.4f}
- 流动性失衡度：{smart_percentage_format(technical_indicators.get('orderbook_liquidity_imbalance', 0) * 100)}
- 买盘趋势：{technical_indicators.get('orderbook_bids_trend', 'stable')}
- 卖盘趋势：{technical_indicators.get('orderbook_asks_trend', 'stable')}

【24小时订单薄历史】
- 24h大额订单总数：{technical_indicators.get('orderbook_24h_total_orders', 0)}个
- 24h完成订单数：{technical_indicators.get('orderbook_24h_completed_orders', 0)}个
- 24h完成率：{smart_percentage_format(technical_indicators.get('orderbook_24h_completion_rate', 0) * 100)}
- 24h完成交易价值：{smart_volume_format(technical_indicators.get('orderbook_24h_completed_value', 0))}

【核心技术指标】

【多周期RSI分析 - 4个时间周期】
- RSI(7)：{technical_indicators.get('rsi_7', 0):.1f} | 信号：{technical_indicators.get('rsi_signal_7', '计算中')}
- RSI(14)：{technical_indicators.get('rsi_14', 0):.1f} | 信号：{technical_indicators.get('rsi_signal_14', '计算中')}
- RSI(21)：{technical_indicators.get('rsi_21', 0):.1f} | 信号：{technical_indicators.get('rsi_signal_21', '计算中')}
- RSI(28)：{technical_indicators.get('rsi_28', 0):.1f} | 信号：{technical_indicators.get('rsi_signal_28', '计算中')}

【多周期布林带分析 - 10个时间周期】
- BB(10)：上轨{smart_price_format(technical_indicators.get('bb_upper_10', 0))} | 中轨{smart_price_format(technical_indicators.get('bb_middle_10', 0))} | 下轨{smart_price_format(technical_indicators.get('bb_lower_10', 0))} | 位置{technical_indicators.get('bb_position_10', 0):.1f}% | 信号{technical_indicators.get('bb_signal_10', '计算中')}
- BB(20)：上轨{smart_price_format(technical_indicators.get('bb_upper_20', 0))} | 中轨{smart_price_format(technical_indicators.get('bb_middle_20', 0))} | 下轨{smart_price_format(technical_indicators.get('bb_lower_20', 0))} | 位置{technical_indicators.get('bb_position_20', 0):.1f}% | 信号{technical_indicators.get('bb_signal_20', '计算中')}
- BB(30)：上轨{smart_price_format(technical_indicators.get('bb_upper_30', 0))} | 中轨{smart_price_format(technical_indicators.get('bb_middle_30', 0))} | 下轨{smart_price_format(technical_indicators.get('bb_lower_30', 0))} | 位置{technical_indicators.get('bb_position_30', 0):.1f}% | 信号{technical_indicators.get('bb_signal_30', '计算中')}
- BB(40)：上轨{smart_price_format(technical_indicators.get('bb_upper_40', 0))} | 中轨{smart_price_format(technical_indicators.get('bb_middle_40', 0))} | 下轨{smart_price_format(technical_indicators.get('bb_lower_40', 0))} | 位置{technical_indicators.get('bb_position_40', 0):.1f}% | 信号{technical_indicators.get('bb_signal_40', '计算中')}
- BB(50)：上轨{smart_price_format(technical_indicators.get('bb_upper_50', 0))} | 中轨{smart_price_format(technical_indicators.get('bb_middle_50', 0))} | 下轨{smart_price_format(technical_indicators.get('bb_lower_50', 0))} | 位置{technical_indicators.get('bb_position_50', 0):.1f}% | 信号{technical_indicators.get('bb_signal_50', '计算中')}
- BB(60)：上轨{smart_price_format(technical_indicators.get('bb_upper_60', 0))} | 中轨{smart_price_format(technical_indicators.get('bb_middle_60', 0))} | 下轨{smart_price_format(technical_indicators.get('bb_lower_60', 0))} | 位置{technical_indicators.get('bb_position_60', 0):.1f}% | 信号{technical_indicators.get('bb_signal_60', '计算中')}
- BB(70)：上轨{smart_price_format(technical_indicators.get('bb_upper_70', 0))} | 中轨{smart_price_format(technical_indicators.get('bb_middle_70', 0))} | 下轨{smart_price_format(technical_indicators.get('bb_lower_70', 0))} | 位置{technical_indicators.get('bb_position_70', 0):.1f}% | 信号{technical_indicators.get('bb_signal_70', '计算中')}
- BB(80)：上轨{smart_price_format(technical_indicators.get('bb_upper_80', 0))} | 中轨{smart_price_format(technical_indicators.get('bb_middle_80', 0))} | 下轨{smart_price_format(technical_indicators.get('bb_lower_80', 0))} | 位置{technical_indicators.get('bb_position_80', 0):.1f}% | 信号{technical_indicators.get('bb_signal_80', '计算中')}
- BB(90)：上轨{smart_price_format(technical_indicators.get('bb_upper_90', 0))} | 中轨{smart_price_format(technical_indicators.get('bb_middle_90', 0))} | 下轨{smart_price_format(technical_indicators.get('bb_lower_90', 0))} | 位置{technical_indicators.get('bb_position_90', 0):.1f}% | 信号{technical_indicators.get('bb_signal_90', '计算中')}
- BB(100)：上轨{smart_price_format(technical_indicators.get('bb_upper_100', 0))} | 中轨{smart_price_format(technical_indicators.get('bb_middle_100', 0))} | 下轨{smart_price_format(technical_indicators.get('bb_lower_100', 0))} | 位置{technical_indicators.get('bb_position_100', 0):.1f}% | 信号{technical_indicators.get('bb_signal_100', '计算中')}

【多周期移动平均线分析 - 9个MA周期 + 10个EMA周期】
- MA(5)：{smart_price_format(technical_indicators.get('ma_5', 0))} | 偏离：{smart_percentage_format(technical_indicators.get('ma_5_diff_percent', 0))}
- MA(10)：{smart_price_format(technical_indicators.get('ma_10', 0))} | 偏离：{smart_percentage_format(technical_indicators.get('ma_10_diff_percent', 0))}
- MA(15)：{smart_price_format(technical_indicators.get('ma_15', 0))} | 偏离：{smart_percentage_format(technical_indicators.get('ma_15_diff_percent', 0))}
- MA(20)：{smart_price_format(technical_indicators.get('ma_20', 0))} | 偏离：{smart_percentage_format(technical_indicators.get('ma_20_diff_percent', 0))}
- MA(25)：{smart_price_format(technical_indicators.get('ma_25', 0))} | 偏离：{smart_percentage_format(technical_indicators.get('ma_25_diff_percent', 0))}
- MA(30)：{smart_price_format(technical_indicators.get('ma_30', 0))} | 偏离：{smart_percentage_format(technical_indicators.get('ma_30_diff_percent', 0))}
- MA(50)：{smart_price_format(technical_indicators.get('ma_50', 0))} | 偏离：{smart_percentage_format(technical_indicators.get('ma_50_diff_percent', 0))}
- MA(100)：{smart_price_format(technical_indicators.get('ma_100', 0))} | 偏离：{smart_percentage_format(technical_indicators.get('ma_100_diff_percent', 0))}
- MA(200)：{smart_price_format(technical_indicators.get('ma_200', 0))} | 偏离：{smart_percentage_format(technical_indicators.get('ma_200_diff_percent', 0))}
- EMA(5)：{smart_price_format(technical_indicators.get('ema_5', 0))} | 偏离：{smart_percentage_format(technical_indicators.get('ema_5_diff_percent', 0))}
- EMA(10)：{smart_price_format(technical_indicators.get('ema_10', 0))} | 偏离：{smart_percentage_format(technical_indicators.get('ema_10_diff_percent', 0))}
- EMA(12)：{smart_price_format(technical_indicators.get('ema_12', 0))} | 偏离：{smart_percentage_format(technical_indicators.get('ema_12_diff_percent', 0))}
- EMA(15)：{smart_price_format(technical_indicators.get('ema_15', 0))} | 偏离：{smart_percentage_format(technical_indicators.get('ema_15_diff_percent', 0))}
- EMA(20)：{smart_price_format(technical_indicators.get('ema_20', 0))} | 偏离：{smart_percentage_format(technical_indicators.get('ema_20_diff_percent', 0))}
- EMA(26)：{smart_price_format(technical_indicators.get('ema_26', 0))} | 偏离：{smart_percentage_format(technical_indicators.get('ema_26_diff_percent', 0))}
- EMA(30)：{smart_price_format(technical_indicators.get('ema_30', 0))} | 偏离：{smart_percentage_format(technical_indicators.get('ema_30_diff_percent', 0))}
- EMA(50)：{smart_price_format(technical_indicators.get('ema_50', 0))} | 偏离：{smart_percentage_format(technical_indicators.get('ema_50_diff_percent', 0))}
- EMA(100)：{smart_price_format(technical_indicators.get('ema_100', 0))} | 偏离：{smart_percentage_format(technical_indicators.get('ema_100_diff_percent', 0))}
- EMA(200)：{smart_price_format(technical_indicators.get('ema_200', 0))} | 偏离：{smart_percentage_format(technical_indicators.get('ema_200_diff_percent', 0))}

【多周期MACD分析 - 3种配置】
- MACD标准(12,26,9)：DIF={technical_indicators.get('macd_12_26_9', 0):.6f} | DEA={technical_indicators.get('macd_signal_12_26_9', 0):.6f} | 柱状图={technical_indicators.get('macd_histogram_12_26_9', 0):.6f} | 趋势：{technical_indicators.get('macd_trend_12_26_9', '计算中')}
- MACD快速(5,35,5)：DIF={technical_indicators.get('macd_5_35_5', 0):.6f} | DEA={technical_indicators.get('macd_signal_5_35_5', 0):.6f} | 柱状图={technical_indicators.get('macd_histogram_5_35_5', 0):.6f} | 趋势：{technical_indicators.get('macd_trend_5_35_5', '计算中')}
- MACD慢速(19,39,9)：DIF={technical_indicators.get('macd_19_39_9', 0):.6f} | DEA={technical_indicators.get('macd_signal_19_39_9', 0):.6f} | 柱状图={technical_indicators.get('macd_histogram_19_39_9', 0):.6f} | 趋势：{technical_indicators.get('macd_trend_19_39_9', '计算中')}

【其他高级技术指标】
- ATR(14)真实波动范围：{smart_price_format(technical_indicators.get('atr', 0))} | 波动率：{smart_percentage_format(technical_indicators.get('atr_percent', 0))}
- ADX(14)趋势强度：{technical_indicators.get('adx', 0):.1f} | 趋势评级：{technical_indicators.get('adx_trend', '计算中')}
- 随机震荡指标(14)：K值={technical_indicators.get('stoch_k', 0):.1f} | D值={technical_indicators.get('stoch_d', 0):.1f} | 信号：{technical_indicators.get('stoch_signal', '计算中')}
- 威廉指标(14)：%R={technical_indicators.get('williams_r', 0):.1f} | 信号：{technical_indicators.get('williams_signal', '计算中')}
- CCI(20)商品通道指标：{technical_indicators.get('cci', 0):.1f} | 信号：{technical_indicators.get('cci_signal', '计算中')}
- MFI(14)资金流量指标：{technical_indicators.get('mfi', 0):.1f} | 信号：{technical_indicators.get('mfi_signal', '计算中')}
- OBV成交量平衡：{smart_volume_format(technical_indicators.get('obv', 0))} | 趋势：{technical_indicators.get('obv_trend', '计算中')}
- 抛物线SAR：{smart_price_format(technical_indicators.get('sar', 0))} | 信号：{technical_indicators.get('sar_signal', '计算中')}
- RSI(14)标准：{rsi:.1f}

【多周期技术指标分析】
* 重要：以下是完整的多周期技术指标数据，必须在报告中详细展示 *
{multi_period_data}

* 以上多周期数据包含了7-28周期RSI、10-100周期布林带、9个周期移动平均线、3种MACD配置和其他高级指标，在生成报告时必须使用 *

【CoinGlass高级市场数据】
* 重要：以下是CoinGlass提供的高级市场数据，包含现货流向、期权流向、多时间周期分析 *
{coinglass_data}

* 请在CoinGlass高级数据分析部分详细使用以上数据 *"""
            
            # 添加基差指标到核心技术指标中（合约市场）
            if market_type == 'futures':
                current_basis = technical_indicators.get('current_basis', 0)
                basis_sentiment = technical_indicators.get('basis_sentiment', '未知')
                prompt += f"""

【期现基差指标】
- 期现基差：{smart_percentage_format(current_basis)} ({basis_sentiment})"""
            
            prompt += f"""

【交易信号分析】"""
            
            # 添加所有交易信号（详细版本）
            signal_categories = {
                'overall_signal': '🎯 综合信号',
                'signal_strength': '   信号强度',
                'rsi_analysis': '📊 RSI分析',
                'macd_analysis': '📈 MACD分析',
                'ma_analysis': '📉 均线分析',
                'basis_analysis': '💰 基差分析',
            }
            
            for signal_type, signal_value in trading_signals.items():
                if signal_type in signal_categories:
                    label = signal_categories[signal_type]
                    prompt += f"\n{label}: {signal_value}"
            
            # 添加详细的布林带信号分析（10个周期）
            bb_signals = [k for k in trading_signals.keys() if k.startswith('bb_analysis_')]
            if bb_signals:
                prompt += f"\n\n📊 多周期布林带信号详细分析 ({len(bb_signals)}个周期):"
                for i, signal_key in enumerate(sorted(bb_signals), 1):
                    period = signal_key.replace('bb_analysis_', '')
                    signal_value = trading_signals[signal_key]
                    prompt += f"\n   BB({period}): {signal_value}"
                    if i % 5 == 0:  # 每5个换行
                        prompt += ""
            
            # 添加其他特殊信号
            other_signals = {}
            for key, value in trading_signals.items():
                if key not in signal_categories and not key.startswith('bb_analysis_'):
                    other_signals[key] = value
            
            if other_signals:
                prompt += f"\n\n🔍 其他技术信号分析:"
                for signal_type, signal_value in other_signals.items():
                    signal_name = signal_type.replace('_', ' ').title()
                    prompt += f"\n   {signal_name}: {signal_value}"
            
            # 添加市场数据摘要
            prompt += f"\n\n【市场深度数据】\n{market_summary}"
            
            # 添加基差数据到技术指标中（如果是合约市场）
            if market_type == 'futures' and technical_indicators.get('current_basis') is not None:
                current_basis = technical_indicators.get('current_basis', 0)
                basis_sentiment = technical_indicators.get('basis_sentiment', '未知')
                arbitrage_opportunity = technical_indicators.get('arbitrage_opportunity', '未知')
                basis_change = technical_indicators.get('basis_change', 0)
                
                prompt += f"\n\n【期现基差数据】"
                prompt += f"\n- 当前基差：{smart_percentage_format(current_basis)}"
                prompt += f"\n- 基差变化：{smart_percentage_format(basis_change)}"
                prompt += f"\n- 市场情绪：{basis_sentiment}"
                prompt += f"\n- 套利机会：{arbitrage_opportunity}"
                prompt += f"\n- 基差解读：{'正基差，合约价格高于现货' if current_basis > 0 else '负基差，合约价格低于现货' if current_basis < 0 else '基差接近零，期现价格基本一致'}"
            
            # 添加支撑阻力位计算数据
            resistance_levels = self._calculate_resistance_levels(current_price, bb_upper, ema_20, sma_50)
            support_levels = self._calculate_support_levels(current_price, bb_lower, ema_20, sma_50)
            
            prompt += f"""
【输出要求】
请严格按照以下分页Markdown模板格式输出专业分析报告：

## 📋 综合评估与关键点位

当前市场摘要： [基于所有提供数据给出直白的总结性摘要，包括主要趋势、资金流向、关键风险，不要显示计算逻辑直接显示结果,如趋势强度: 强 (ADX: 32.7)的 (ADX: 32.7)，只显示强]

核心观点： [明确的看涨/看跌/震荡判断/开仓/止损/止盈/平仓位置，不要显示计算逻辑直接显示结果]

关键价位： 
- 🎯 买入参考：[基于技术支撑和资金流向，不要显示计算逻辑直接显示结果]
- 🎯 卖出参考：[基于技术阻力和资金流向，不要显示计算逻辑直接显示结果]  
- 🛑 止损位置：[基于风险控制，不要显示计算逻辑直接显示结果]


【关键价位计算】
- 阻力位参考：R1 {smart_price_format(resistance_levels[0])}, R2 {smart_price_format(resistance_levels[1])}, R3 {smart_price_format(resistance_levels[2])}
- 支撑位参考：S1 {smart_price_format(support_levels[0])}, S2 {smart_price_format(support_levels[1])}, S3 {smart_price_format(support_levels[2])}


主力资金态度： [基于订单薄和流向数据判断，不要显示计算逻辑直接显示结果]

---

### 📈 交易策略建议

多头策略
- 区间: [请基于技术指标计算具体区间，不要显示计算逻辑直接显示结果]
- 止损: [请基于支撑位给出具体价格，不要显示计算逻辑直接显示结果]
- 目标: [请基于阻力位给出具体价格，不要显示计算逻辑直接显示结果]

空头策略
- 区间: [请基于技术指标计算具体区间，不要显示计算逻辑直接显示结果]
- 止损: [请基于阻力位给出具体价格，不要显示计算逻辑直接显示结果]
- 目标: [请基于支撑位给出具体价格，不要显示计算逻辑直接显示结果]

⚠️ 风险提示: [请基于当前技术指标给出具体风险提示]：

---

### 💹 主力资金行为解读

- 成交量变化: {smart_percentage_format(volume_change)}
- 价格动能: [请基于RSI和MACD分析，不要显示计算逻辑直接显示结果]
- 资金流向: [请基于技术指标判断，不要显示计算逻辑直接显示结果]
> 解读: [请提供具体的资金行为分析，不要显示计算逻辑直接显示结果]

---

### 📍 关键价位

| 阻力位 | | 支撑位 | |
| :--- | :--- | :--- | :--- |
| R1 | {smart_price_format(resistance_levels[0])} | S1 | {smart_price_format(support_levels[0])} |
| R2 | {smart_price_format(resistance_levels[1])} | S2 | {smart_price_format(support_levels[1])} |
| R3 | {smart_price_format(resistance_levels[2])} | S3 | {smart_price_format(support_levels[2])} |

> 解读: [请基于价位分析提供具体建议]

---

### 💬 市场情绪"""
            
            # 添加基差分析部分（仅合约市场）
            if market_type == 'futures':
                prompt += f"""

- 期现基差: [请分析当前基差水平，正负基差判断]
- 基差趋势: [请分析基差变化趋势和市场意义]
- 套利机会: [请评估期现套利的可行性和风险]
- 情绪指标: 基差反映的市场多空情绪和预期"""
            
            prompt += f"""
- RSI指标: {rsi:.1f} [超买/超卖/正常区间]
- 技术面情绪: [请基于多个指标综合判断]
- 短期情绪: [请基于价格和成交量分析]

> 解读: [请提供具体的市场情绪分析]

---


### 📊 多周期技术指标详细分析

多周期RSI分析 (7, 14, 21, 28周期)：
[请基于提供的多周期RSI数据进行详细分析，包含每个周期的数值、信号和解读]

多周期布林带分析 (10-100周期)：
[请基于提供的10个布林带周期数据进行详细分析，包含位置百分比、带宽和突破信号]

多周期移动平均线分析 (9个周期)：
[请基于提供的MA和EMA多周期数据进行详细分析，包含5, 10, 15, 20, 25, 30, 50, 100, 200周期]

多周期MACD分析 (3种配置)：
[请基于提供的标准、快速、慢速MACD配置进行详细分析，包含DIF、DEA、柱状图值]

其他高级指标分析：
[请基于提供的ATR、ADX、随机震荡、威廉指标、CCI、MFI、OBV等指标数据进行详细分析]

> 多周期信号综合解读: [请基于所有多周期指标提供综合的市场判断和趋势分析]

---

### 💰 高级市场数据分析

资金流向分析 (8个时间周期)：
[请基于【币安合约市场完整数据】【基础市场数据】的数据分析5分钟、15分钟、30分钟、1小时、4小时、12小时、24小时、1周的买卖量流向]


期权流向分析：
[请基于期权持仓量变化和成交量数据分析机构和散户情绪，包含期权OI变化、成交量变化、净流向，如果该币种没有对应的期权数据，则不显示这一段]

多时间周期价格动量：
[请基于【币安合约市场完整数据】【基础市场数据】的数据分析8个时间周期的价格变化数据分析短中长期动量，识别趋势转换点]



> 综合数据解读: [请请基于【币安合约市场完整数据】【基础市场数据】、期权、多时间周期数据提供市场主力行为和资金流向的深度分析]

---

### 📋 订单薄深度分析

大额订单行为分析：
[请基于大额订单数据分析主力资金意图，包含买单卖单数量对比、价值分析、最大订单规模解读]

订单薄流动性评估：
[请基于买盘卖盘深度金额和数量分析市场流动性状况，评估大单对价格的潜在冲击]

买卖压力不平衡分析：
[请基于买卖比率和流动性失衡度分析市场多空力量对比，识别潜在价格方向]

订单薄趋势监控：
[请基于买盘卖盘趋势变化分析市场情绪演变，判断资金进出信号]

24小时订单完成率分析：
[请基于24小时订单完成情况分析市场执行效率和资金流动性，评估交易活跃度]

> 订单薄综合解读: [请综合大额订单、深度数据、趋势变化提供订单薄层面的市场微观结构分析]

---

请确保：
1. 所有数值都基于提供的真实数据计算
2. 必须使用【多周期技术指标分析】中提供的详细数据
3. 多周期指标分析部分不得省略，必须包含所有提供的周期数据
4. 必须使用【高级市场数据】进行现货流向、期权流向（如果对应币种的期权数据没有就不显示）、多时间周期分析，来自【币安合约市场完整数据】和【现货全市场数据】
5. 必须使用【订单薄数据】进行大额订单、深度数据、历史完成率分析
6. 高级数据分析、订单薄深度分析部分必须详细分析
7. 交易策略要给出具体价格区间，结合技术指标，市场综合数据，资金流向和订单薄数据，但是不显示具体的原理，只给结果和点位，不要显示原理
8. 风险提示要针对当前市场状况、流向异常和订单薄失衡情况
9. 解读部分要深入分析，结合多维度数据，不能空泛
10. 严格按照Markdown格式输出，模拟三页显示效果
11. 特别注意：多周期RSI、布林带、移动平均线、MACD等数据已在上文提供
12. 重点关注：现货和期权的8个时间周期资金流向数据必须进行深度分析，来自【币安合约市场完整数据】和【现货全市场数据】，如果对应币种的期权数据没有就不显示期权只分析现货
13. 订单薄分析要求：必须分析大额订单行为、流动性评估、买卖压力不平衡、趋势监控、完成率分析
14. 价格格式：自动去除尾随零，例如$2.9548800000显示为$2.95488
"""

            return prompt
            
        except Exception as e:
            logger.error(f"❌ 构建专业模板提示词失败: {str(e)}")
            return self._get_fallback_prompt(symbol, market_type)
    
    def _calculate_resistance_levels(self, current_price: float, bb_upper: float, 
                                   ema_20: float, sma_50: float) -> List[float]:
        """计算阻力位"""
        levels = []
        
        # R1: 最近高点或布林带上轨
        r1 = max(bb_upper, current_price * 1.02)
        levels.append(r1)
        
        # R2: 更强的技术阻力位
        r2 = max(ema_20 * 1.05, current_price * 1.05)
        levels.append(r2)
        
        # R3: 长期阻力位
        r3 = max(sma_50 * 1.08, current_price * 1.08)
        levels.append(r3)
        
        return sorted(levels)
    
    def _calculate_support_levels(self, current_price: float, bb_lower: float,
                                ema_20: float, sma_50: float) -> List[float]:
        """计算支撑位"""
        levels = []
        
        # S1: 最近低点或布林带下轨
        s1 = min(bb_lower, current_price * 0.98)
        levels.append(s1)
        
        # S2: 更强的技术支撑位
        s2 = min(ema_20 * 0.95, current_price * 0.95)
        levels.append(s2)
        
        # S3: 长期支撑位
        s3 = min(sma_50 * 0.92, current_price * 0.92)
        levels.append(s3)
        
        return sorted(levels, reverse=True)
    
    def _extract_market_summary(self, symbol: str, market_data: Dict[str, Any]) -> str:
        """提取市场摘要信息"""
        try:
            # 从market_data中查找对应币种的数据
            coin_data = None
            symbol_base = symbol.replace('USDT', '').upper()
            
            # 查找匹配的币种数据
            if isinstance(market_data, dict):
                for key, value in market_data.items():
                    if isinstance(value, list):
                        for item in value:
                            if isinstance(item, dict) and item.get('symbol', '').upper() == symbol_base:
                                coin_data = item
                                break
                    if coin_data:
                        break
            
            if not coin_data:
                return f"💰 市场数据：{symbol} 的详细市场数据暂时不可用"
            
            # 格式化市场数据
            summary_parts = [f"💰 {symbol} 市场数据摘要："]
            
            # 价格信息
            if 'current_price' in coin_data:
                summary_parts.append(f"  当前价格: {smart_price_format(coin_data['current_price'])}")
            
            # 市值信息
            if 'market_cap_usd' in coin_data:
                market_cap = coin_data['market_cap_usd']
                if market_cap > 1e9:
                    summary_parts.append(f"  市值: {smart_price_format(market_cap/1e9)}B")
                elif market_cap > 1e6:
                    summary_parts.append(f"  市值: {smart_price_format(market_cap/1e6)}M")
            
            # 持仓量信息
            if 'open_interest_usd' in coin_data:
                oi = coin_data['open_interest_usd']
                if oi > 1e9:
                    summary_parts.append(f"  持仓量: {smart_volume_format(oi/1e9)}B")
                elif oi > 1e6:
                    summary_parts.append(f"  持仓量: {smart_volume_format(oi/1e6)}M")
            
            # 资金费率
            if 'avg_funding_rate_by_oi' in coin_data:
                funding_rate = coin_data['avg_funding_rate_by_oi'] * 100
                summary_parts.append(f"  资金费率: {smart_percentage_format(funding_rate)}")
            
            # 24小时涨跌幅
            if 'price_change_percent_24h' in coin_data:
                change_24h = coin_data['price_change_percent_24h']
                direction = "📈" if change_24h > 0 else "📉" if change_24h < 0 else "➡️"
                summary_parts.append(f"  24h涨跌幅: {direction} {smart_percentage_format(change_24h)}")
            
            # 持仓量24小时变化
            if 'open_interest_change_percent_24h' in coin_data:
                oi_change = coin_data['open_interest_change_percent_24h']
                direction = "⬆️" if oi_change > 0 else "⬇️" if oi_change < 0 else "➡️"
                summary_parts.append(f"  持仓量24h变化: {direction} {smart_percentage_format(oi_change)}")
            
            # 多空比
            if 'long_short_ratio_24h' in coin_data:
                ls_ratio = coin_data['long_short_ratio_24h']
                summary_parts.append(f"  多空比(24h): {smart_percentage_format(ls_ratio)}")
            
            return "\n".join(summary_parts)
            
        except Exception as e:
            logger.warning(f"提取市场摘要失败: {str(e)}")
            return f"💰 {symbol} 市场数据处理中..."
    
    def _format_kline_summary(self, df: pd.DataFrame, interval: str) -> str:
        """格式化K线数据摘要"""
        try:
            if df is None or len(df) == 0:
                return "📊 K线数据：暂无可用数据"
            
            current_price = df['close'].iloc[-1]
            prev_price = df['close'].iloc[-2] if len(df) >= 2 else current_price
            price_change = current_price - prev_price
            price_change_percent = (price_change / prev_price * 100) if prev_price != 0 else 0
            
            # 最近几根K线的趋势
            recent_trend = self._analyze_recent_trend(df)
            
            # 价格区间
            period_high = df['high'].max()
            period_low = df['low'].min()
            price_position = ((current_price - period_low) / (period_high - period_low) * 100) if period_high != period_low else 50
            
            # 成交量分析
            current_volume = df['volume_usd'].iloc[-1]
            avg_volume = df['volume_usd'].mean()
            volume_ratio = current_volume / avg_volume if avg_volume > 0 else 1
            
            summary = f"""📊 K线数据分析 ({interval}周期，{len(df)}根K线)：
  当前价格: {smart_price_format(current_price)} ({smart_percentage_format(price_change)}, {smart_percentage_format(price_change_percent)})
  价格区间: {smart_price_format(period_low)} - {smart_price_format(period_high)}
  价格位置: {smart_percentage_format(price_position)}% (在周期区间内的位置)
  近期趋势: {recent_trend}
  当前成交量: {smart_volume_format(current_volume)} (平均值的 {smart_percentage_format(volume_ratio)}倍)
  数据时间: {df.index[0].strftime('%m-%d %H:%M')} 至 {df.index[-1].strftime('%m-%d %H:%M')}"""
            
            return summary
            
        except Exception as e:
            logger.warning(f"格式化K线摘要失败: {str(e)}")
            return "📊 K线数据：处理中..."
    
    def _analyze_recent_trend(self, df: pd.DataFrame) -> str:
        """分析最近的价格趋势"""
        try:
            if len(df) < 3:
                return "数据不足"
            
            # 最近3-5根K线的收盘价
            recent_closes = df['close'].tail(5).values
            
            # 计算连续上涨/下跌
            up_count = 0
            down_count = 0
            
            for i in range(1, len(recent_closes)):
                if recent_closes[i] > recent_closes[i-1]:
                    up_count += 1
                    down_count = 0
                elif recent_closes[i] < recent_closes[i-1]:
                    down_count += 1
                    up_count = 0
            
            if up_count >= 3:
                return f"连续上涨{up_count}根K线"
            elif down_count >= 3:
                return f"连续下跌{down_count}根K线"
            elif up_count >= 2:
                return f"短期上涨{up_count}根K线"
            elif down_count >= 2:
                return f"短期下跌{down_count}根K线"
            else:
                return "震荡整理"
                
        except:
            return "趋势分析中"
    
    def _format_technical_indicators(self, indicators: Dict[str, Any]) -> str:
        """格式化技术指标"""
        try:
            if not indicators:
                return "🔧 技术指标：计算中..."
            
            sections = []
            
            # 趋势指标
            trend_indicators = []
            for period in [20, 50, 200]:
                ma_key = f'ma_{period}'
                ma_diff_key = f'ma_{period}_diff_percent'
                if ma_key in indicators and ma_diff_key in indicators:
                    ma_value = indicators[ma_key]
                    diff_percent = indicators[ma_diff_key]
                    direction = "上方" if diff_percent > 0 else "下方"
                    trend_indicators.append(f"MA{period}: {smart_price_format(ma_value)} (价格在{direction}{smart_percentage_format(abs(diff_percent))})")
            
            if trend_indicators:
                sections.append("📈 趋势指标：\n  " + "\n  ".join(trend_indicators))
            
            # 动量指标
            momentum_indicators = []
            if 'rsi' in indicators:
                rsi_signal = indicators.get('rsi_signal', '')
                momentum_indicators.append(f"RSI: {indicators['rsi']:.1f} ({rsi_signal})")
            
            if 'macd' in indicators and 'macd_signal' in indicators:
                macd_trend = indicators.get('macd_trend', '')
                momentum_indicators.append(f"MACD: {indicators['macd']:.4f} ({macd_trend})")
            
            if 'stoch_k' in indicators:
                stoch_signal = indicators.get('stoch_signal', '')
                momentum_indicators.append(f"随机指标: K={indicators['stoch_k']:.1f} ({stoch_signal})")
            
            if momentum_indicators:
                sections.append("⚡ 动量指标：\n  " + "\n  ".join(momentum_indicators))
            
            # 波动率指标
            volatility_indicators = []
            if 'bb_upper' in indicators and 'bb_lower' in indicators:
                bb_signal = indicators.get('bb_signal', '')
                bb_position = indicators.get('bb_position', 0)
                volatility_indicators.append(f"布林带: {smart_price_format(indicators['bb_lower'])} - {smart_price_format(indicators['bb_upper'])}")
                volatility_indicators.append(f"布林带位置: {smart_percentage_format(bb_position)}% ({bb_signal})")
            
            if 'atr' in indicators and 'atr_percent' in indicators:
                volatility_indicators.append(f"ATR: {smart_price_format(indicators['atr'])} ({smart_percentage_format(indicators['atr_percent'])}%)")
            
            if volatility_indicators:
                sections.append("📊 波动率指标：\n  " + "\n  ".join(volatility_indicators))
            
            # 成交量指标
            volume_indicators = []
            if 'volume_ratio' in indicators:
                volume_signal = indicators.get('volume_signal', '')
                volume_indicators.append(f"成交量比率: {smart_percentage_format(indicators['volume_ratio'])}倍 ({volume_signal})")
            
            if 'obv_trend' in indicators:
                volume_indicators.append(f"OBV趋势: {indicators['obv_trend']}")
            
            if volume_indicators:
                sections.append("📶 成交量指标：\n  " + "\n  ".join(volume_indicators))
            
            # 基差指标（合约市场）
            if 'current_basis' in indicators:
                basis_indicators = []
                current_basis = indicators['current_basis']
                basis_sentiment = indicators.get('basis_sentiment', '未知')
                arbitrage_opportunity = indicators.get('arbitrage_opportunity', '未知')
                basis_change = indicators.get('basis_change', 0)
                
                basis_indicators.append(f"当前基差: {smart_percentage_format(current_basis)} ({basis_sentiment})")
                basis_indicators.append(f"基差变化: {smart_percentage_format(basis_change)}")
                basis_indicators.append(f"套利机会: {arbitrage_opportunity}")
                
                sections.append("🔮 期现基差：\n  " + "\n  ".join(basis_indicators))
            
            return "\n\n".join(sections) if sections else "🔧 技术指标：计算中..."
            
        except Exception as e:
            logger.warning(f"格式化技术指标失败: {str(e)}")
            return "🔧 技术指标：处理中..."
    
    def _format_multi_period_indicators(self, indicators: Dict[str, Any]) -> str:
        """格式化多周期技术指标（扩展版本，包含所有详细数据）+ CoinGlass高级数据"""
        try:
            if not indicators:
                return "📊 多周期分析：计算中..."
            
            sections = []
            
            # 详细的多周期RSI分析
            rsi_periods = [7, 14, 21, 28]
            rsi_data = []
            rsi_details = []
            for period in rsi_periods:
                if f'rsi_{period}' in indicators:
                    rsi_value = indicators[f'rsi_{period}']
                    rsi_signal = indicators.get(f'rsi_signal_{period}', '中性')
                    signal_emoji = "🔴" if rsi_signal == "超买" else "🟢" if rsi_signal == "超卖" else "⚪"
                    rsi_data.append(f"RSI({period}): {rsi_value:.1f} {signal_emoji}")
                    
                    # 详细分析
                    if rsi_value > 70:
                        analysis = f"RSI({period})={rsi_value:.1f}，处于超买区域，存在回调风险"
                    elif rsi_value < 30:
                        analysis = f"RSI({period})={rsi_value:.1f}，处于超卖区域，存在反弹机会"
                    else:
                        analysis = f"RSI({period})={rsi_value:.1f}，处于正常区间，动能适中"
                    rsi_details.append(f"   - {analysis}")
            
            if rsi_data:
                rsi_section = "📊 多周期RSI分析：\n" + " | ".join(rsi_data)
                if rsi_details:
                    rsi_section += "\n" + "\n".join(rsi_details)
                sections.append(rsi_section)
            
            # 详细的多周期布林带分析（10个周期完整展示）
            bb_periods = list(range(10, 101, 10))  # 10, 20, 30, ..., 100
            bb_data = []
            bb_details = []
            current_price = indicators.get('current_price', 0)
            
            for period in bb_periods:
                if f'bb_signal_{period}' in indicators:
                    bb_signal = indicators[f'bb_signal_{period}']
                    bb_position = indicators.get(f'bb_position_{period}', 50)
                    bb_upper = indicators.get(f'bb_upper_{period}', 0)
                    bb_lower = indicators.get(f'bb_lower_{period}', 0)
                    bb_middle = indicators.get(f'bb_middle_{period}', 0)
                    
                    position_emoji = "🔴" if bb_position > 80 else "🟢" if bb_position < 20 else "⚪"
                    bb_data.append(f"BB({period}): {bb_position:.0f}% {position_emoji}")
                    
                    # 详细分析
                    width = ((bb_upper - bb_lower) / bb_middle * 100) if bb_middle > 0 else 0
                    if bb_position > 80:
                        analysis = f"BB({period}): 价格{smart_price_format(current_price)}接近上轨{smart_price_format(bb_upper)}，带宽{smart_percentage_format(width)}%，可能回调"
                    elif bb_position < 20:
                        analysis = f"BB({period}): 价格{smart_price_format(current_price)}接近下轨{smart_price_format(bb_lower)}，带宽{smart_percentage_format(width)}%，可能反弹"
                    else:
                        analysis = f"BB({period}): 价格{smart_price_format(current_price)}在中轨{smart_price_format(bb_middle)}附近，带宽{smart_percentage_format(width)}%，震荡状态"
                    bb_details.append(f"   - {analysis}")
            
            if bb_data:
                bb_section = "📊 多周期布林带分析（10-100周期）：\n" + " | ".join(bb_data)
                if bb_details:
                    bb_section += "\n" + "\n".join(bb_details)
                sections.append(bb_section)
            
            # 详细的多周期移动平均线分析
            ma_periods = [5, 10, 15, 20, 25, 30, 50, 100, 200]
            ma_data = []
            ma_details = []
            ema_data = []
            ema_details = []
            
            for period in ma_periods:
                # MA分析
                if f'ma_{period}_diff_percent' in indicators:
                    diff_percent = indicators[f'ma_{period}_diff_percent']
                    ma_value = indicators.get(f'ma_{period}', 0)
                    direction_emoji = "🟢" if diff_percent > 0 else "🔴" if diff_percent < 0 else "⚪"
                    ma_data.append(f"MA({period}): {smart_percentage_format(diff_percent)} {direction_emoji}")
                    
                    trend = "上方" if diff_percent > 0 else "下方" if diff_percent < 0 else "附近"
                    ma_details.append(f"   - MA({period})={smart_price_format(ma_value)}，价格在均线{trend}{smart_percentage_format(abs(diff_percent))}")
            
            # EMA分析
            ema_periods = [5, 10, 12, 15, 20, 26, 30, 50, 100, 200]
            for period in ema_periods:
                if f'ema_{period}_diff_percent' in indicators:
                    diff_percent = indicators[f'ema_{period}_diff_percent']
                    ema_value = indicators.get(f'ema_{period}', 0)
                    direction_emoji = "🟢" if diff_percent > 0 else "🔴" if diff_percent < 0 else "⚪"
                    ema_data.append(f"EMA({period}): {smart_percentage_format(diff_percent)} {direction_emoji}")
                    
                    trend = "上方" if diff_percent > 0 else "下方" if diff_percent < 0 else "附近"
                    ema_details.append(f"   - EMA({period})={smart_price_format(ema_value)}，价格在均线{trend}{smart_percentage_format(abs(diff_percent))}")
            
            if ma_data:
                ma_section = "📊 多周期移动平均线分析：\n" + " | ".join(ma_data)
                if ma_details:
                    ma_section += "\n" + "\n".join(ma_details)
                sections.append(ma_section)
            
            if ema_data:
                ema_section = "📊 多周期指数移动平均线分析：\n" + " | ".join(ema_data)
                if ema_details:
                    ema_section += "\n" + "\n".join(ema_details)
                sections.append(ema_section)
            
            # 详细的多周期MACD分析
            macd_configs = [
                {'fast': 12, 'slow': 26, 'signal': 9, 'name': '标准'},
                {'fast': 5, 'slow': 35, 'signal': 5, 'name': '快速'},
                {'fast': 19, 'slow': 39, 'signal': 9, 'name': '慢速'}
            ]
            macd_data = []
            macd_details = []
            
            for config in macd_configs:
                key = f"{config['fast']}_{config['slow']}_{config['signal']}"
                if f'macd_trend_{key}' in indicators:
                    trend = indicators[f'macd_trend_{key}']
                    macd_value = indicators.get(f'macd_{key}', 0)
                    signal_value = indicators.get(f'macd_signal_{key}', 0)
                    histogram = indicators.get(f'macd_histogram_{key}', 0)
                    
                    trend_emoji = "🟢" if trend == "看涨" else "🔴" if trend == "看跌" else "⚪"
                    macd_data.append(f"MACD({config['name']}): {trend} {trend_emoji}")
                    
                    # 详细分析
                    if macd_value > signal_value:
                        signal_desc = "MACD线高于信号线，动能偏多"
                    elif macd_value < signal_value:
                        signal_desc = "MACD线低于信号线，动能偏空"
                    else:
                        signal_desc = "MACD线接近信号线，动能转换"
                    
                    macd_details.append(f"   - MACD({config['fast']},{config['slow']},{config['signal']}): DIF={smart_percentage_format(macd_value)}, DEA={smart_percentage_format(signal_value)}, 柱状图={smart_percentage_format(histogram)}, {signal_desc}")
            
            if macd_data:
                macd_section = "📊 多周期MACD分析：\n" + " | ".join(macd_data)
                if macd_details:
                    macd_section += "\n" + "\n".join(macd_details)
                sections.append(macd_section)
            
            # 添加其他技术指标详细信息
            other_indicators = []
            
            # ATR指标
            if 'atr' in indicators:
                atr_value = indicators['atr']
                atr_percent = (atr_value / current_price * 100) if current_price > 0 else 0
                other_indicators.append(f"   - ATR真实波动范围: {smart_price_format(atr_value)} ({smart_percentage_format(atr_percent)}%)")
            
            # ADX趋势强度
            if 'adx' in indicators:
                adx_value = indicators['adx']
                if adx_value > 50:
                    adx_desc = "极强趋势"
                elif adx_value > 25:
                    adx_desc = "强趋势"
                else:
                    adx_desc = "弱趋势或震荡"
                other_indicators.append(f"   - ADX趋势强度: {adx_value:.1f} ({adx_desc})")
            
            # 随机震荡指标
            if 'stoch_k' in indicators and 'stoch_d' in indicators:
                k_value = indicators['stoch_k']
                d_value = indicators['stoch_d']
                stoch_signal = "超买" if k_value > 80 else "超卖" if k_value < 20 else "正常"
                other_indicators.append(f"   - 随机震荡指标: K={k_value:.1f}, D={d_value:.1f} ({stoch_signal})")
            
            # 威廉指标
            if 'williams_r' in indicators:
                wr_value = indicators['williams_r']
                wr_signal = "超买" if wr_value > -20 else "超卖" if wr_value < -80 else "正常"
                other_indicators.append(f"   - 威廉指标: %R={wr_value:.1f} ({wr_signal})")
            
            # CCI指标
            if 'cci' in indicators:
                cci_value = indicators['cci']
                cci_signal = "超买" if cci_value > 100 else "超卖" if cci_value < -100 else "正常"
                other_indicators.append(f"   - CCI商品通道指标: {cci_value:.1f} ({cci_signal})")
            
            # MFI资金流量指标
            if 'mfi' in indicators:
                mfi_value = indicators['mfi']
                mfi_signal = "超买" if mfi_value > 80 else "超卖" if mfi_value < 20 else "正常"
                other_indicators.append(f"   - MFI资金流量指标: {mfi_value:.1f} ({mfi_signal})")
            
            # OBV成交量平衡指标
            if 'obv' in indicators:
                obv_value = indicators['obv']
                obv_trend = indicators.get('obv_trend', '未知')
                other_indicators.append(f"   - OBV成交量平衡: {obv_value:.0f} (趋势: {obv_trend})")
            
            if other_indicators:
                sections.append("📊 其他技术指标详细分析：\n" + "\n".join(other_indicators))
            
            # 综合多周期信号强度（扩展版）
            signal_strength = self._calculate_multi_period_signal_strength(indicators)
            if signal_strength:
                # 添加详细的信号统计
                bullish_signals = []
                bearish_signals = []
                neutral_signals = []
                
                # 统计各类信号
                for key, value in indicators.items():
                    if key.endswith('_signal'):
                        signal_type = key.replace('_signal', '')
                        if isinstance(value, str):
                            if any(word in value.lower() for word in ['买入', '看涨', '超卖', '上涨']):
                                bullish_signals.append(f"{signal_type}: {value}")
                            elif any(word in value.lower() for word in ['卖出', '看跌', '超买', '下跌']):
                                bearish_signals.append(f"{signal_type}: {value}")
                            else:
                                neutral_signals.append(f"{signal_type}: {value}")
                
                signal_detail = f"📊 多周期信号强度统计：{signal_strength}\n"
                if bullish_signals:
                    signal_detail += f"   🟢 看涨信号 ({len(bullish_signals)}个): " + ", ".join(bullish_signals[:5]) + ("..." if len(bullish_signals) > 5 else "") + "\n"
                if bearish_signals:
                    signal_detail += f"   🔴 看跌信号 ({len(bearish_signals)}个): " + ", ".join(bearish_signals[:5]) + ("..." if len(bearish_signals) > 5 else "") + "\n"
                if neutral_signals:
                    signal_detail += f"   ⚪ 中性信号 ({len(neutral_signals)}个): " + ", ".join(neutral_signals[:3]) + ("..." if len(neutral_signals) > 3 else "")
                
                sections.append(signal_detail)
            
            # 添加CoinGlass高级数据格式化
            coinglass_section = self._format_coinglass_data(indicators)
            if coinglass_section:
                sections.append(coinglass_section)
            
            return "\n\n".join(sections) if sections else "📊 多周期分析：计算中..."
            
        except Exception as e:
            logger.warning(f"格式化多周期指标失败: {str(e)}")
            return "📊 多周期分析：处理中..."
    
    def _calculate_multi_period_signal_strength(self, indicators: Dict[str, Any]) -> str:
        """计算多周期信号强度"""
        try:
            bullish_count = 0
            bearish_count = 0
            total_signals = 0
            
            # RSI信号统计
            for period in [7, 14, 21, 28]:
                if f'rsi_signal_{period}' in indicators:
                    signal = indicators[f'rsi_signal_{period}']
                    total_signals += 1
                    if signal == "超卖":
                        bullish_count += 1
                    elif signal == "超买":
                        bearish_count += 1
            
            # MACD信号统计
            for config in [{'fast': 12, 'slow': 26, 'signal': 9}, {'fast': 5, 'slow': 35, 'signal': 5}, {'fast': 19, 'slow': 39, 'signal': 9}]:
                key = f"{config['fast']}_{config['slow']}_{config['signal']}"
                if f'macd_trend_{key}' in indicators:
                    trend = indicators[f'macd_trend_{key}']
                    total_signals += 1
                    if trend == "看涨":
                        bullish_count += 1
                    elif trend == "看跌":
                        bearish_count += 1
            
            # 布林带信号统计
            for period in list(range(10, 101, 10)):  # 10, 20, 30, ..., 100
                if f'bb_signal_{period}' in indicators:
                    signal = indicators[f'bb_signal_{period}']
                    total_signals += 1
                    if "超卖" in signal:
                        bullish_count += 1
                    elif "超买" in signal:
                        bearish_count += 1
            
            # 移动平均线信号统计
            for period in [5, 10, 20, 50, 100]:
                if f'ma_{period}_diff_percent' in indicators:
                    diff = indicators[f'ma_{period}_diff_percent']
                    total_signals += 1
                    if diff > 1:
                        bullish_count += 1
                    elif diff < -1:
                        bearish_count += 1
            
            if total_signals == 0:
                return ""
            
            bullish_ratio = (bullish_count / total_signals) * 100
            bearish_ratio = (bearish_count / total_signals) * 100
            neutral_ratio = 100 - bullish_ratio - bearish_ratio
            
            # 信号强度判断
            if bullish_ratio > 60:
                strength = "🟢 强烈看涨"
            elif bullish_ratio > 40:
                strength = "🟢 偏向看涨"
            elif bearish_ratio > 60:
                strength = "🔴 强烈看跌"
            elif bearish_ratio > 40:
                strength = "🔴 偏向看跌"
            else:
                strength = "⚪ 中性震荡"
            
            return f"{strength} (看涨{smart_percentage_format(bullish_ratio)}% | 中性{smart_percentage_format(neutral_ratio)}% | 看跌{smart_percentage_format(bearish_ratio)}%)"
            
        except Exception as e:
            logger.warning(f"计算多周期信号强度失败: {str(e)}")
            return ""
    
    def _format_coinglass_data(self, indicators: Dict[str, Any], symbol: str = None) -> str:
        """格式化CoinGlass高级数据并提取字段到indicators中"""
        try:
            coinglass_sections = []
            
            # 提取现货数据到indicators中（传递symbol参数）
            self._extract_spot_data_to_indicators(indicators, symbol)
            
            # 提取期权数据到indicators中  
            self._extract_option_data_to_indicators(indicators)
            
            # 提取合约数据到indicators中（传递symbol参数）
            self._extract_futures_data_to_indicators(indicators, symbol)
            
            # 提取订单薄数据到indicators中
            if symbol:
                self._extract_orderbook_data_to_indicators(indicators, symbol)
            
            # 添加现货流向数据格式化（传递symbol参数）
            spot_flow_section = self._format_spot_flow_data(symbol)
            if spot_flow_section:
                coinglass_sections.append(spot_flow_section)
            
            # 添加期权流向数据格式化（传递symbol参数）
            option_flow_section = self._format_option_flow_data(symbol)
            if option_flow_section:
                coinglass_sections.append(option_flow_section)
            
            # 添加多时间周期分析
            multi_timeframe_section = self._format_multi_timeframe_analysis()
            if multi_timeframe_section:
                coinglass_sections.append(multi_timeframe_section)
            
            # 如果没有数据，提供描述性信息
            if not coinglass_sections:
                return """💰 CoinGlass高级市场数据说明：
- 现货资金流向数据：包含5分钟、15分钟、30分钟、1小时、4小时、12小时、24小时、1周的买卖量、净流向、流向比率
- 期权流向数据：包含期权持仓量变化、成交量变化、净资金流向、流向比率  
- 多时间周期价格变化：8个时间周期的价格涨跌幅数据，用于分析短中长期动量
- 市场深度数据：评估流动性、资金承接能力和价格影响
- 跨交易所数据比较：Binance、OKX等主要交易所的数据对比"""
            
            return "\n\n".join(coinglass_sections)
                
        except Exception as e:
            logger.warning(f"格式化CoinGlass数据失败: {str(e)}")
            return "💰 CoinGlass高级数据：处理中..."
    
    def _get_latest_coinglass_data_dir(self) -> str:
        """获取最新的CoinGlass数据目录"""
        try:
            import glob
            data_dirs = glob.glob("data/coinglass/2025*")
            if data_dirs:
                return max(data_dirs)  # 获取最新的目录
            return None
        except Exception:
            return None
    
    def _extract_spot_data_to_indicators(self, indicators: Dict[str, Any], symbol: str = None) -> None:
        """从CoinGlass现货数据中提取所有详细字段到indicators中"""
        try:
            latest_dir = self._get_latest_coinglass_data_dir()
            if not latest_dir:
                return
            
            spot_cache_file = os.path.join(latest_dir, "spot.json")
            if os.path.exists(spot_cache_file):
                with open(spot_cache_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    
                # 查找指定币种数据，如果没有指定则默认查找BTC
                target_symbol = symbol.replace('USDT', '').upper() if symbol else 'BTC'
                spot_data = None
                for item in data:
                    if item.get('symbol') == target_symbol:
                        spot_data = item
                        break
                        
                if not spot_data:
                    return
                    
                # 基础价格数据
                indicators['coinglass_spot_current_price'] = spot_data.get('current_price', 0)
                indicators['coinglass_spot_market_cap'] = spot_data.get('market_cap', 0)
                
                # 价格变化数据 - 8个时间周期
                indicators['coinglass_spot_price_change_5m'] = spot_data.get('price_change_5m', 0)
                indicators['coinglass_spot_price_change_15m'] = spot_data.get('price_change_15m', 0)
                indicators['coinglass_spot_price_change_30m'] = spot_data.get('price_change_30m', 0)
                indicators['coinglass_spot_price_change_1h'] = spot_data.get('price_change_1h', 0)
                indicators['coinglass_spot_price_change_4h'] = spot_data.get('price_change_4h', 0)
                indicators['coinglass_spot_price_change_12h'] = spot_data.get('price_change_12h', 0)
                indicators['coinglass_spot_price_change_24h'] = spot_data.get('price_change_24h', 0)
                indicators['coinglass_spot_price_change_1w'] = spot_data.get('price_change_1w', 0)
                
                # 价格变化百分比 - 8个时间周期
                indicators['coinglass_spot_price_change_percent_5m'] = spot_data.get('price_change_percent_5m', 0)
                indicators['coinglass_spot_price_change_percent_15m'] = spot_data.get('price_change_percent_15m', 0)
                indicators['coinglass_spot_price_change_percent_30m'] = spot_data.get('price_change_percent_30m', 0)
                indicators['coinglass_spot_price_change_percent_1h'] = spot_data.get('price_change_percent_1h', 0)
                indicators['coinglass_spot_price_change_percent_4h'] = spot_data.get('price_change_percent_4h', 0)
                indicators['coinglass_spot_price_change_percent_12h'] = spot_data.get('price_change_percent_12h', 0)
                indicators['coinglass_spot_price_change_percent_24h'] = spot_data.get('price_change_percent_24h', 0)
                indicators['coinglass_spot_price_change_percent_1w'] = spot_data.get('price_change_percent_1w', 0)
                
                # 成交量数据 - 8个时间周期
                indicators['coinglass_spot_volume_5m'] = spot_data.get('volume_usd_5m', 0)
                indicators['coinglass_spot_volume_15m'] = spot_data.get('volume_usd_15m', 0)
                indicators['coinglass_spot_volume_30m'] = spot_data.get('volume_usd_30m', 0)
                indicators['coinglass_spot_volume_1h'] = spot_data.get('volume_usd_1h', 0)
                indicators['coinglass_spot_volume_4h'] = spot_data.get('volume_usd_4h', 0)
                indicators['coinglass_spot_volume_12h'] = spot_data.get('volume_usd_12h', 0)
                indicators['coinglass_spot_volume_24h'] = spot_data.get('volume_usd_24h', 0)
                indicators['coinglass_spot_volume_1w'] = spot_data.get('volume_usd_1w', 0)
                
                # 成交量变化数据 - 8个时间周期
                indicators['coinglass_spot_volume_change_5m'] = spot_data.get('volume_change_usd_5m', 0)
                indicators['coinglass_spot_volume_change_15m'] = spot_data.get('volume_change_usd_15m', 0)
                indicators['coinglass_spot_volume_change_30m'] = spot_data.get('volume_change_usd_30m', 0)
                indicators['coinglass_spot_volume_change_1h'] = spot_data.get('volume_change_usd_1h', 0)
                indicators['coinglass_spot_volume_change_4h'] = spot_data.get('volume_change_usd_4h', 0)
                indicators['coinglass_spot_volume_change_12h'] = spot_data.get('volume_change_usd_12h', 0)
                indicators['coinglass_spot_volume_change_24h'] = spot_data.get('volume_change_usd_24h', 0)
                indicators['coinglass_spot_volume_change_1w'] = spot_data.get('volume_change_usd_1w', 0)
                
                # 成交量变化百分比 - 8个时间周期
                indicators['coinglass_spot_volume_change_percent_5m'] = spot_data.get('volume_change_percent_5m', 0)
                indicators['coinglass_spot_volume_change_percent_15m'] = spot_data.get('volume_change_percent_15m', 0)
                indicators['coinglass_spot_volume_change_percent_30m'] = spot_data.get('volume_change_percent_30m', 0)
                indicators['coinglass_spot_volume_change_percent_1h'] = spot_data.get('volume_change_percent_1h', 0)
                indicators['coinglass_spot_volume_change_percent_4h'] = spot_data.get('volume_change_percent_4h', 0)
                indicators['coinglass_spot_volume_change_percent_12h'] = spot_data.get('volume_change_percent_12h', 0)
                indicators['coinglass_spot_volume_change_percent_24h'] = spot_data.get('volume_change_percent_24h', 0)
                indicators['coinglass_spot_volume_change_percent_1w'] = spot_data.get('volume_change_percent_1w', 0)
                
                # 买卖量数据 - 8个时间周期
                indicators['coinglass_spot_buy_volume_5m'] = spot_data.get('buy_volume_usd_5m', 0)
                indicators['coinglass_spot_buy_volume_15m'] = spot_data.get('buy_volume_usd_15m', 0)
                indicators['coinglass_spot_buy_volume_30m'] = spot_data.get('buy_volume_usd_30m', 0)
                indicators['coinglass_spot_buy_volume_1h'] = spot_data.get('buy_volume_usd_1h', 0)
                indicators['coinglass_spot_buy_volume_4h'] = spot_data.get('buy_volume_usd_4h', 0)
                indicators['coinglass_spot_buy_volume_12h'] = spot_data.get('buy_volume_usd_12h', 0)
                indicators['coinglass_spot_buy_volume_24h'] = spot_data.get('buy_volume_usd_24h', 0)
                indicators['coinglass_spot_buy_volume_1w'] = spot_data.get('buy_volume_usd_1w', 0)
                
                indicators['coinglass_spot_sell_volume_5m'] = spot_data.get('sell_volume_usd_5m', 0)
                indicators['coinglass_spot_sell_volume_15m'] = spot_data.get('sell_volume_usd_15m', 0)
                indicators['coinglass_spot_sell_volume_30m'] = spot_data.get('sell_volume_usd_30m', 0)
                indicators['coinglass_spot_sell_volume_1h'] = spot_data.get('sell_volume_usd_1h', 0)
                indicators['coinglass_spot_sell_volume_4h'] = spot_data.get('sell_volume_usd_4h', 0)
                indicators['coinglass_spot_sell_volume_12h'] = spot_data.get('sell_volume_usd_12h', 0)
                indicators['coinglass_spot_sell_volume_24h'] = spot_data.get('sell_volume_usd_24h', 0)
                indicators['coinglass_spot_sell_volume_1w'] = spot_data.get('sell_volume_usd_1w', 0)
                
                # 资金流向数据 - 8个时间周期
                indicators['coinglass_spot_volume_flow_5m'] = spot_data.get('volume_flow_usd_5m', 0)
                indicators['coinglass_spot_volume_flow_15m'] = spot_data.get('volume_flow_usd_15m', 0)
                indicators['coinglass_spot_volume_flow_30m'] = spot_data.get('volume_flow_usd_30m', 0)
                indicators['coinglass_spot_volume_flow_1h'] = spot_data.get('volume_flow_usd_1h', 0)
                indicators['coinglass_spot_volume_flow_4h'] = spot_data.get('volume_flow_usd_4h', 0)
                indicators['coinglass_spot_volume_flow_12h'] = spot_data.get('volume_flow_usd_12h', 0)
                indicators['coinglass_spot_volume_flow_24h'] = spot_data.get('volume_flow_usd_24h', 0)
                indicators['coinglass_spot_volume_flow_1w'] = spot_data.get('volume_flow_usd_1w', 0)
                
        except Exception as e:
            # 数据读取失败时使用默认值
            pass
    
    def _extract_option_data_to_indicators(self, indicators: Dict[str, Any]) -> None:
        """从CoinGlass期权数据中提取所有详细字段到indicators中"""
        try:
            option_cache_file = "data/cache/coinglass/option_flow_data.json"
            if os.path.exists(option_cache_file):
                with open(option_cache_file, 'r', encoding='utf-8') as f:
                    option_data = json.load(f)
                    
                    # 期权基础数据
                    indicators['coinglass_option_oi'] = option_data.get('open_interest', 0)
                    indicators['coinglass_option_volume'] = option_data.get('volume', 0)
                    indicators['coinglass_option_oi_change'] = option_data.get('oi_change_24h', 0)
                    indicators['coinglass_option_volume_change'] = option_data.get('volume_change_24h', 0)
                    indicators['coinglass_option_net_flow'] = option_data.get('net_flow', 0)
                    indicators['coinglass_option_flow_ratio'] = option_data.get('flow_ratio', 1.0)
                    indicators['coinglass_option_spot_ratio'] = option_data.get('option_spot_ratio', 0)
                    
        except Exception as e:
            # 数据读取失败时使用默认值
            pass
    
    def _extract_orderbook_data_to_indicators(self, indicators: Dict[str, Any], symbol: str) -> None:
        """从AI订单薄管理器中提取订单薄数据到indicators中"""
        try:
            if not self.orderbook_manager:
                return
            
            # 获取综合订单薄数据
            orderbook_data = self.orderbook_manager.get_comprehensive_orderbook_data(symbol)
            
            if orderbook_data:
                # 当前大额订单数据
                current_orders = orderbook_data.get('current_large_orders', {})
                indicators['orderbook_large_orders_count'] = current_orders.get('count', 0)
                indicators['orderbook_buy_orders_count'] = current_orders.get('buy_orders', 0)
                indicators['orderbook_sell_orders_count'] = current_orders.get('sell_orders', 0)
                indicators['orderbook_total_buy_value'] = current_orders.get('total_buy_value', 0)
                indicators['orderbook_total_sell_value'] = current_orders.get('total_sell_value', 0)
                indicators['orderbook_largest_buy_order'] = current_orders.get('largest_buy_order', 0)
                indicators['orderbook_largest_sell_order'] = current_orders.get('largest_sell_order', 0)
                indicators['orderbook_avg_buy_price'] = current_orders.get('avg_buy_price', 0)
                indicators['orderbook_avg_sell_price'] = current_orders.get('avg_sell_price', 0)
                
                # 订单薄深度数据
                depth_data = orderbook_data.get('orderbook_depth', {})
                indicators['orderbook_latest_bids_usd'] = depth_data.get('latest_bids_usd', 0)
                indicators['orderbook_latest_asks_usd'] = depth_data.get('latest_asks_usd', 0)
                indicators['orderbook_latest_bids_quantity'] = depth_data.get('latest_bids_quantity', 0)
                indicators['orderbook_latest_asks_quantity'] = depth_data.get('latest_asks_quantity', 0)
                indicators['orderbook_bid_ask_ratio'] = depth_data.get('bid_ask_ratio', 0)
                indicators['orderbook_liquidity_imbalance'] = depth_data.get('liquidity_imbalance', 0)
                indicators['orderbook_bids_trend'] = depth_data.get('bids_trend', 'stable')
                indicators['orderbook_asks_trend'] = depth_data.get('asks_trend', 'stable')
                
                # 24小时历史数据
                history_data = orderbook_data.get('24h_history', {})
                indicators['orderbook_24h_total_orders'] = history_data.get('total_large_orders', 0)
                indicators['orderbook_24h_completed_orders'] = history_data.get('completed_orders', 0)
                indicators['orderbook_24h_completion_rate'] = history_data.get('completion_rate', 0)
                indicators['orderbook_24h_completed_value'] = history_data.get('total_completed_value', 0)
                
        except Exception as e:
            logger.warning(f"提取订单薄数据失败: {str(e)}")
            # 设置默认值
            pass
    
    def _extract_futures_data_to_indicators(self, indicators: Dict[str, Any], symbol: str = None) -> None:
        """从CoinGlass合约数据中提取所有详细字段到indicators中"""
        try:
            latest_dir = self._get_latest_coinglass_data_dir()
            if not latest_dir:
                return
            
            futures_cache_file = os.path.join(latest_dir, "futures.json")
            if os.path.exists(futures_cache_file):
                with open(futures_cache_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    
                # 查找指定币种数据，如果没有指定则默认查找BTC
                target_symbol = symbol.replace('USDT', '').upper() if symbol else 'BTC'
                futures_data = None
                for item in data:
                    if item.get('symbol') == target_symbol:
                        futures_data = item
                        break
                        
                if not futures_data:
                    return
                    
                # 合约基础数据
                indicators['coinglass_futures_current_price'] = futures_data.get('current_price', 0)
                indicators['coinglass_futures_market_cap_ratio'] = futures_data.get('open_interest_market_cap_ratio', 0)
                indicators['coinglass_futures_oi_usd'] = futures_data.get('open_interest_usd', 0)
                indicators['coinglass_futures_oi_quantity'] = futures_data.get('open_interest_quantity', 0)
                indicators['coinglass_futures_oi_volume_ratio'] = futures_data.get('oi_vol_ratio', 0)
                    
                # 资金费率数据
                indicators['coinglass_futures_avg_funding_rate_by_oi'] = futures_data.get('avg_funding_rate_by_oi', 0)
                indicators['coinglass_futures_avg_funding_rate_by_vol'] = futures_data.get('avg_funding_rate_by_vol', 0)
                
                # 持仓成交比变化数据
                indicators['coinglass_futures_oi_vol_ratio_change_1h'] = futures_data.get('oi_vol_ratio_change_percent_1h', 0)
                indicators['coinglass_futures_oi_vol_ratio_change_4h'] = futures_data.get('oi_vol_ratio_change_percent_4h', 0)
                indicators['coinglass_futures_oi_vol_ratio_change_24h'] = futures_data.get('oi_vol_ratio_change_percent_24h', 0)
                
                # 价格变化数据 - 7个时间周期
                indicators['coinglass_futures_price_change_percent_5m'] = futures_data.get('price_change_percent_5m', 0)
                indicators['coinglass_futures_price_change_percent_15m'] = futures_data.get('price_change_percent_15m', 0)
                indicators['coinglass_futures_price_change_percent_30m'] = futures_data.get('price_change_percent_30m', 0)
                indicators['coinglass_futures_price_change_percent_1h'] = futures_data.get('price_change_percent_1h', 0)
                indicators['coinglass_futures_price_change_percent_4h'] = futures_data.get('price_change_percent_4h', 0)
                indicators['coinglass_futures_price_change_percent_12h'] = futures_data.get('price_change_percent_12h', 0)
                indicators['coinglass_futures_price_change_percent_24h'] = futures_data.get('price_change_percent_24h', 0)
                
                # 持仓量变化数据 - 6个时间周期
                indicators['coinglass_futures_oi_change_percent_5m'] = futures_data.get('open_interest_change_percent_5m', 0)
                indicators['coinglass_futures_oi_change_percent_15m'] = futures_data.get('open_interest_change_percent_15m', 0)
                indicators['coinglass_futures_oi_change_percent_30m'] = futures_data.get('open_interest_change_percent_30m', 0)
                indicators['coinglass_futures_oi_change_percent_1h'] = futures_data.get('open_interest_change_percent_1h', 0)
                indicators['coinglass_futures_oi_change_percent_4h'] = futures_data.get('open_interest_change_percent_4h', 0)
                indicators['coinglass_futures_oi_change_percent_24h'] = futures_data.get('open_interest_change_percent_24h', 0)
                
                indicators['coinglass_futures_oi_change_usd_5m'] = futures_data.get('open_interest_change_usd_5m', 0)
                indicators['coinglass_futures_oi_change_usd_15m'] = futures_data.get('open_interest_change_usd_15m', 0)
                indicators['coinglass_futures_oi_change_usd_30m'] = futures_data.get('open_interest_change_usd_30m', 0)
                indicators['coinglass_futures_oi_change_usd_1h'] = futures_data.get('open_interest_change_usd_1h', 0)
                indicators['coinglass_futures_oi_change_usd_4h'] = futures_data.get('open_interest_change_usd_4h', 0)
                indicators['coinglass_futures_oi_change_usd_24h'] = futures_data.get('open_interest_change_usd_24h', 0)
                
                # 成交量变化数据 - 3个时间周期
                indicators['coinglass_futures_volume_change_percent_1h'] = futures_data.get('volume_change_percent_1h', 0)
                indicators['coinglass_futures_volume_change_percent_4h'] = futures_data.get('volume_change_percent_4h', 0)
                indicators['coinglass_futures_volume_change_percent_24h'] = futures_data.get('volume_change_percent_24h', 0)
                
                indicators['coinglass_futures_volume_change_usd_1h'] = futures_data.get('volume_change_usd_1h', 0)
                indicators['coinglass_futures_volume_change_usd_4h'] = futures_data.get('volume_change_usd_4h', 0)
                indicators['coinglass_futures_volume_change_usd_24h'] = futures_data.get('volume_change_usd_24h', 0)
                
                # 多空比数据 - 7个时间周期
                indicators['coinglass_futures_long_short_ratio_5m'] = futures_data.get('long_short_ratio_5m', 1.0)
                indicators['coinglass_futures_long_short_ratio_15m'] = futures_data.get('long_short_ratio_15m', 1.0)
                indicators['coinglass_futures_long_short_ratio_30m'] = futures_data.get('long_short_ratio_30m', 1.0)
                indicators['coinglass_futures_long_short_ratio_1h'] = futures_data.get('long_short_ratio_1h', 1.0)
                indicators['coinglass_futures_long_short_ratio_4h'] = futures_data.get('long_short_ratio_4h', 1.0)
                indicators['coinglass_futures_long_short_ratio_12h'] = futures_data.get('long_short_ratio_12h', 1.0)
                indicators['coinglass_futures_long_short_ratio_24h'] = futures_data.get('long_short_ratio_24h', 1.0)
                
                # 多空成交量数据 - 7个时间周期
                indicators['coinglass_futures_long_volume_5m'] = futures_data.get('long_volume_usd_5m', 0)
                indicators['coinglass_futures_long_volume_15m'] = futures_data.get('long_volume_usd_15m', 0)
                indicators['coinglass_futures_long_volume_30m'] = futures_data.get('long_volume_usd_30m', 0)
                indicators['coinglass_futures_long_volume_1h'] = futures_data.get('long_volume_usd_1h', 0)
                indicators['coinglass_futures_long_volume_4h'] = futures_data.get('long_volume_usd_4h', 0)
                indicators['coinglass_futures_long_volume_12h'] = futures_data.get('long_volume_usd_12h', 0)
                indicators['coinglass_futures_long_volume_24h'] = futures_data.get('long_volume_usd_24h', 0)
                
                indicators['coinglass_futures_short_volume_5m'] = futures_data.get('short_volume_usd_5m', 0)
                indicators['coinglass_futures_short_volume_15m'] = futures_data.get('short_volume_usd_15m', 0)
                indicators['coinglass_futures_short_volume_30m'] = futures_data.get('short_volume_usd_30m', 0)
                indicators['coinglass_futures_short_volume_1h'] = futures_data.get('short_volume_usd_1h', 0)
                indicators['coinglass_futures_short_volume_4h'] = futures_data.get('short_volume_usd_4h', 0)
                indicators['coinglass_futures_short_volume_12h'] = futures_data.get('short_volume_usd_12h', 0)
                indicators['coinglass_futures_short_volume_24h'] = futures_data.get('short_volume_usd_24h', 0)
            
                # 爆仓数据 - 4个时间周期
                indicators['coinglass_futures_liquidation_1h'] = futures_data.get('liquidation_usd_1h', 0)
                indicators['coinglass_futures_liquidation_4h'] = futures_data.get('liquidation_usd_4h', 0)
                indicators['coinglass_futures_liquidation_12h'] = futures_data.get('liquidation_usd_12h', 0)
                indicators['coinglass_futures_liquidation_24h'] = futures_data.get('liquidation_usd_24h', 0)
                
                indicators['coinglass_futures_long_liquidation_1h'] = futures_data.get('long_liquidation_usd_1h', 0)
                indicators['coinglass_futures_long_liquidation_4h'] = futures_data.get('long_liquidation_usd_4h', 0)
                indicators['coinglass_futures_long_liquidation_12h'] = futures_data.get('long_liquidation_usd_12h', 0)
                indicators['coinglass_futures_long_liquidation_24h'] = futures_data.get('long_liquidation_usd_24h', 0)
                
                indicators['coinglass_futures_short_liquidation_1h'] = futures_data.get('short_liquidation_usd_1h', 0)
                indicators['coinglass_futures_short_liquidation_4h'] = futures_data.get('short_liquidation_usd_4h', 0)
                indicators['coinglass_futures_short_liquidation_12h'] = futures_data.get('short_liquidation_usd_12h', 0)
                indicators['coinglass_futures_short_liquidation_24h'] = futures_data.get('short_liquidation_usd_24h', 0)
                    
        except Exception as e:
            # 数据读取失败时使用默认值
            pass
    
    def _format_spot_flow_data(self, symbol: str = None) -> str:
        """格式化现货流向数据"""
        try:
            # 尝试从CoinGlass缓存目录读取最新现货数据
            coinglass_cache_dir = "data/coinglass"
            if os.path.exists(coinglass_cache_dir):
                # 获取最新的缓存目录
                cache_dirs = [d for d in os.listdir(coinglass_cache_dir) 
                             if os.path.isdir(os.path.join(coinglass_cache_dir, d))]
                
                if cache_dirs:
                    # 按时间排序，获取最新的
                    cache_dirs.sort(reverse=True)
                    latest_dir = os.path.join(coinglass_cache_dir, cache_dirs[0])
                    spot_file = os.path.join(latest_dir, "spot.json")
                    
                    if os.path.exists(spot_file):
                        with open(spot_file, 'r', encoding='utf-8') as f:
                            spot_data = json.load(f)
                        
                        if spot_data:
                            # 确定要分析的币种
                            if symbol:
                                # 分析用户查询的具体币种
                                target_symbol = symbol.replace('USDT', '').upper()
                                analysis_lines = []
                                
                                # 查找指定币种的数据
                                for coin_data in spot_data:
                                    coin_symbol = coin_data.get('symbol', '').upper()
                                    if coin_symbol == target_symbol:
                                        # 提取多时间周期数据
                                        flow_analysis = []
                                        periods = ['5m', '15m', '30m', '1h', '4h', '12h', '24h', '1w']
                                        
                                        for period in periods:
                                            buy_volume_key = f'buy_volume_usd_{period}'
                                            sell_volume_key = f'sell_volume_usd_{period}'
                                            
                                            if buy_volume_key in coin_data and sell_volume_key in coin_data:
                                                buy_vol = coin_data.get(buy_volume_key, 0)
                                                sell_vol = coin_data.get(sell_volume_key, 0)
                                                net_flow = buy_vol - sell_vol
                                                
                                                if buy_vol + sell_vol > 0:
                                                    flow_ratio = buy_vol / (buy_vol + sell_vol)
                                                    flow_trend = "流入" if net_flow > 0 else "流出"
                                                    flow_strength = "强" if abs(flow_ratio - 0.5) > 0.1 else "弱"
                                                    flow_analysis.append(f"{period}:{flow_trend}({flow_ratio:.3f})")
                                        
                                        if flow_analysis:
                                            analysis_lines.append(f"   - {target_symbol}: {' | '.join(flow_analysis)}")
                                            
                                            # 添加详细解读
                                            recent_flows = flow_analysis[:4]  # 最近4个周期
                                            inflow_count = sum(1 for item in recent_flows if "流入" in item)
                                            analysis_lines.append(f"   📊 {target_symbol}资金流向解读: 近期{len(recent_flows)}个周期中有{inflow_count}个周期资金净流入")
                                        break
                                
                                if analysis_lines:
                                    return f"📊 {target_symbol} 现货资金流向分析（8个时间周期）：\n" + "\n".join(analysis_lines)
                                else:
                                    return f"📊 {target_symbol} 现货资金流向：暂无数据或数据处理中..."
                            else:
                                # 没有指定币种时，返回说明信息
                                return "📊 现货资金流向分析：请指定要分析的币种"
            
            # 如果没有读取到数据，返回备用信息
            return """📊 现货资金流向数据包含：
- 8个时间周期：5分钟、15分钟、30分钟、1小时、4小时、12小时、24小时、1周
- 买卖量数据：每个周期的买入和卖出成交量（USD计价）
- 净流向计算：买入量-卖出量，正值表示资金净流入
- 流向比率：买入量/(买入量+卖出量)，>0.5表示买盘占优"""
            
        except Exception as e:
            logger.warning(f"格式化现货流向数据失败: {str(e)}")
            return "📊 现货资金流向：数据处理中..."
    
    def _format_option_flow_data(self, symbol: str = None) -> str:
        """格式化期权流向数据"""
        try:
            # 尝试从缓存读取期权流向数据
            cache_file = os.path.join("data", "cache", "coinglass_option_flow_data.json")
            if os.path.exists(cache_file):
                with open(cache_file, 'r', encoding='utf-8') as f:
                    cache_data = json.load(f)
                    option_data = cache_data.get('data', [])
                    
                if option_data:
                    analysis_lines = []
                    
                    if symbol:
                        # 分析用户查询的具体币种
                        target_symbol = symbol.replace('USDT', '').upper()
                        
                        for option_item in option_data:
                            option_symbol = option_item.get('symbol', '').replace('USDT', '').upper()
                            if option_symbol == target_symbol:
                                oi_change = option_item.get('oi_change_24h', 0)
                                volume_change = option_item.get('volume_change_24h', 0)
                                net_flow = option_item.get('net_flow_usd', 0)
                                
                                flow_direction = "流入" if net_flow > 0 else "流出"
                                oi_trend = "增长" if oi_change > 0 else "减少"
                                volume_trend = "增长" if volume_change > 0 else "减少"
                                
                                analysis_lines.append(f"   - {target_symbol}: OI{oi_trend}({oi_change:+.1f}%), 成交量{volume_trend}({volume_change:+.1f}%), 净{flow_direction}${abs(net_flow)/1e6:.1f}M")
                                break
                        
                        if analysis_lines:
                            return f"📊 {target_symbol} 期权流向分析：\n" + "\n".join(analysis_lines)
                        else:
                            return f"📊 {target_symbol} 期权流向：该币种暂无期权数据"
                    else:
                        # 没有指定币种时，返回说明信息
                        return "📊 期权流向分析：请指定要分析的币种"
            
            # 如果没有读取到数据，返回备用信息
            return """📊 期权流向数据包含：
- 期权持仓量变化(OI Change)：24小时内期权持仓的增减情况
- 期权成交量变化(Volume Change)：24小时内期权交易活跃度变化
- 净资金流向(Net Flow)：期权市场的资金流入流出情况
- 期权/现货比率：反映机构投资者的套期保值和投机行为"""
            
        except Exception as e:
            logger.warning(f"格式化期权流向数据失败: {str(e)}")
            return "📊 期权流向：数据处理中..."
    
    def _format_multi_timeframe_analysis(self) -> str:
        """格式化多时间周期分析"""
        try:
            # 从CoinGlass数据中分析不同时间周期的价格动量和资金流向
            return """📊 多时间周期动量分析：
   - 短期动量(5m-30m): 超短线资金流向，识别日内交易机会和异动信号
   - 中短期动量(1h-4h): 短线趋势确认，适合短线交易策略制定
   - 中期动量(12h-24h): 日级别趋势分析，识别中期方向性机会
   - 长期动量(1w): 周级别趋势确认，适合中长期投资决策
   - 跨周期共振: 多个时间周期信号同向时，趋势延续概率较高
   - 周期背离: 不同时间周期信号分化时，可能预示趋势转换"""
            
        except Exception as e:
            logger.warning(f"格式化多时间周期分析失败: {str(e)}")
            return "📊 多时间周期：数据处理中..."
    
    def _format_trading_signals(self, signals: Dict[str, str]) -> str:
        """格式化交易信号"""
        try:
            if not signals:
                return "🚦 交易信号：分析中..."
            
            sections = []
            
            # 综合信号
            if 'overall_signal' in signals:
                overall = signals['overall_signal']
                strength = signals.get('signal_strength', '')
                sections.append(f"🎯 综合信号: {overall}")
                if strength:
                    sections.append(f"   信号强度: {strength}")
            
            # 具体信号分析
            signal_details = []
            signal_mapping = {
                'rsi_analysis': 'RSI分析',
                'macd_analysis': 'MACD分析', 
                'bb_analysis': '布林带分析',
                'ma_analysis': '均线分析'
            }
            
            for key, label in signal_mapping.items():
                if key in signals:
                    signal_details.append(f"  {label}: {signals[key]}")
            
            if signal_details:
                sections.append("📋 具体信号：\n" + "\n".join(signal_details))
            
            return "\n\n".join(sections) if sections else "🚦 交易信号：分析中..."
            
        except Exception as e:
            logger.warning(f"格式化交易信号失败: {str(e)}")
            return "🚦 交易信号：处理中..."
    
    def _get_fallback_prompt(self, symbol: str, market_type: str) -> str:
        """获取备用提示词（当数据不完整时）"""
        return f"""你是一位专业的数字货币分析师，请对 {symbol} 进行基础分析。

当前分析的币种是 {symbol} ({"合约" if market_type == "futures" else "现货"}市场)。

由于部分数据暂时不可用，请基于你的专业知识提供以下分析：

1. 该币种的基本面分析
2. 当前市场环境下的风险因素
3. 一般性的投资建议和风险提示

请保持客观中性的分析态度，提供有价值的投资参考。"""
    
    def build_quick_analysis_prompt(self, symbol: str, current_price: float, 
                                  price_change_percent: float) -> str:
        """构建快速分析提示词（数据不完整时使用）"""
        try:
            direction = "上涨" if price_change_percent > 0 else "下跌" if price_change_percent < 0 else "持平"
            
            prompt = f"""请对 {symbol} 进行快速分析：

当前价格: {smart_price_format(current_price)}
价格变化: {smart_percentage_format(price_change_percent)} ({direction})

请简要分析：
1. 当前价格走势的可能原因
2. 短期技术面看法
3. 投资风险提示

请保持专业和客观的分析态度。"""
            
            return prompt
            
        except Exception as e:
            logger.error(f"构建快速分析提示词失败: {str(e)}")
            return f"请分析 {symbol} 的当前市场表现和投资前景。"


def test_prompt_builder():
    """测试提示词构建器"""
    import numpy as np
    
    # 创建示例数据
    builder = AIPromptBuilder()
    
    # 示例K线数据
    dates = pd.date_range('2024-01-01', periods=50, freq='H')
    df = pd.DataFrame({
        'open': np.random.rand(50) * 100 + 50000,
        'high': np.random.rand(50) * 100 + 50100,
        'low': np.random.rand(50) * 100 + 49900,
        'close': np.random.rand(50) * 100 + 50000,
        'volume_usd': np.random.rand(50) * 1000000
    }, index=dates)
    
    # 示例技术指标
    indicators = {
        'current_price': 50000.0,
        'rsi': 65.5,
        'rsi_signal': '中性',
        'macd': 0.0012,
        'macd_signal': 0.0008,
        'macd_trend': '看涨',
        'ma_20': 49800.0,
        'ma_20_diff_percent': 0.4,
        'bb_upper': 51000.0,
        'bb_lower': 49000.0,
        'bb_signal': '正常区间'
    }
    
    # 示例市场数据
    market_data = {
        'futures': [{
            'symbol': 'BTC',
            'current_price': 50000.0,
            'market_cap_usd': 1000000000000,
            'open_interest_usd': 10000000000,
            'avg_funding_rate_by_oi': 0.0001,
            'price_change_percent_24h': 2.5,
            'open_interest_change_percent_24h': -1.2,
            'long_short_ratio_24h': 1.05
        }]
    }
    
    # 示例交易信号
    signals = {
        'overall_signal': '偏向看涨',
        'signal_strength': '看涨信号3个，看跌信号1个',
        'rsi_analysis': 'RSI中性',
        'macd_analysis': 'MACD金叉，看涨信号'
    }

# 在模块末尾：如果成功导入了主模块的函数，则使用它们
if _imported_funcs.get('smart_price_format'):
    smart_price_format = _imported_funcs['smart_price_format']
if _imported_funcs.get('smart_percentage_format'):
    smart_percentage_format = _imported_funcs['smart_percentage_format']
if _imported_funcs.get('smart_volume_format'):
    smart_volume_format = _imported_funcs['smart_volume_format']
if _imported_funcs.get('format_beijing_time'):
    format_beijing_time = _imported_funcs['format_beijing_time']
if _imported_funcs.get('get_beijing_time'):
    get_beijing_time = _imported_funcs['get_beijing_time']


if __name__ == "__main__":
    test_prompt_builder() 