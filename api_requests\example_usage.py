#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
订单薄和RSI数据模块使用示例
展示如何使用新增的币安订单薄数据获取和CoinGlass RSI数据获取功能
"""

import sys
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from api_requests.binance.binance_orderbook_requester import BinanceOrderbookRequester
from api_requests.coinglass.coinglass_get_rsi_list import CoinglassRSIListRequester

def example_orderbook_usage():
    """订单薄数据使用示例"""
    print("📊 币安订单薄数据使用示例")
    print("-" * 40)
    
    # 创建请求器实例
    requester = BinanceOrderbookRequester()
    
    # 示例1: 获取单个交易对的合约订单薄
    print("\n1. 获取BTCUSDT合约订单薄 (深度100)")
    success = requester.fetch_futures_orderbook("BTCUSDT", 100)
    if success:
        print("   ✅ 获取成功")
        
        # 加载并分析数据
        data = requester.get_latest_orderbook_data("BTCUSDT", "futures")
        if data:
            bids = data.get('bids', [])
            asks = data.get('asks', [])
            
            print(f"   📈 买单数量: {len(bids)}")
            print(f"   📉 卖单数量: {len(asks)}")
            
            if bids and asks:
                best_bid = float(bids[0][0])  # 最高买价
                best_ask = float(asks[0][0])  # 最低卖价
                spread = best_ask - best_bid
                spread_pct = (spread / best_bid) * 100
                
                print(f"   💰 最高买价: ${best_bid:,.2f}")
                print(f"   💰 最低卖价: ${best_ask:,.2f}")
                print(f"   📊 价差: ${spread:.2f} ({spread_pct:.4f}%)")
    else:
        print("   ❌ 获取失败")
    
    # 示例2: 获取现货订单薄
    print("\n2. 获取ETHUSDT现货订单薄 (深度50)")
    success = requester.fetch_spot_orderbook("ETHUSDT", 50)
    if success:
        print("   ✅ 获取成功")
    else:
        print("   ❌ 获取失败")
    
    # 示例3: 批量获取多个交易对
    print("\n3. 批量获取多个交易对的合约订单薄")
    symbols = ["BTCUSDT", "ETHUSDT", "BNBUSDT", "ADAUSDT"]
    results = requester.fetch_multiple_orderbooks(symbols, "futures", 20)

    print("   批量获取结果:")
    for symbol, success in results.items():
        status = "✅" if success else "❌"
        print(f"   {status} {symbol}")

    # 示例4: 获取所有币种的数据
    print("\n4. 获取所有币种的订单薄数据")
    all_data = requester.get_all_symbols_data("futures", 600)  # 10分钟内的数据
    print(f"   📊 获取到 {len(all_data)} 个币种的合约订单薄数据")

    if all_data:
        # 分析所有币种的价差
        spreads = {}
        for symbol, data in all_data.items():
            bids = data.get('bids', [])
            asks = data.get('asks', [])
            if bids and asks:
                best_bid = float(bids[0][0])
                best_ask = float(asks[0][0])
                spread_pct = ((best_ask - best_bid) / best_bid) * 100
                spreads[symbol] = spread_pct

        # 显示价差最小的3个币种
        if spreads:
            sorted_spreads = sorted(spreads.items(), key=lambda x: x[1])
            print("   💰 价差最小的3个币种:")
            for i, (symbol, spread) in enumerate(sorted_spreads[:3]):
                print(f"      {i+1}. {symbol}: {spread:.4f}%")

    # 示例5: 获取币种列表
    print("\n5. 获取已存储数据的币种列表")
    futures_symbols = requester.get_symbols_list("futures")
    spot_symbols = requester.get_symbols_list("spot")

    print(f"   📋 合约币种: {len(futures_symbols)} 个")
    print(f"   📋 现货币种: {len(spot_symbols)} 个")

    if futures_symbols:
        print(f"   合约币种示例: {', '.join(futures_symbols[:5])}")
    if spot_symbols:
        print(f"   现货币种示例: {', '.join(spot_symbols[:5])}")

def example_rsi_usage():
    """RSI数据使用示例"""
    print("\n📈 CoinGlass RSI数据使用示例")
    print("-" * 40)
    
    # 创建请求器实例
    requester = CoinglassRSIListRequester()
    
    # 获取RSI数据
    print("\n1. 获取所有币种的RSI数据")
    success = requester.get_rsi_data()
    if success:
        print("   ✅ 获取成功")
        
        # 加载并分析数据
        success_load, data, message = requester.file_manager.load_latest_api_data(requester.api_identifier)
        if success_load and data:
            rsi_data = data.get('data', [])
            print(f"   📊 获取到 {len(rsi_data)} 个币种的RSI数据")
            
            # 分析RSI数据
            analyze_rsi_data(rsi_data)
        else:
            print(f"   ❌ 数据加载失败: {message}")
    else:
        print("   ❌ 获取失败")

def analyze_rsi_data(rsi_data):
    """分析RSI数据"""
    if not rsi_data:
        return
    
    print("\n   📈 RSI数据分析:")
    
    # 统计不同RSI区间的币种数量
    oversold_1h = []  # RSI < 30 (超卖)
    overbought_1h = []  # RSI > 70 (超买)
    neutral_1h = []  # 30 <= RSI <= 70 (中性)
    
    oversold_24h = []
    overbought_24h = []
    neutral_24h = []
    
    for coin in rsi_data:
        symbol = coin.get('symbol', 'Unknown')
        rsi_1h = coin.get('rsi1h')
        rsi_24h = coin.get('rsi24h')
        
        # 分析1小时RSI
        if rsi_1h is not None:
            try:
                rsi_1h_val = float(rsi_1h)
                if rsi_1h_val < 30:
                    oversold_1h.append((symbol, rsi_1h_val))
                elif rsi_1h_val > 70:
                    overbought_1h.append((symbol, rsi_1h_val))
                else:
                    neutral_1h.append((symbol, rsi_1h_val))
            except (ValueError, TypeError):
                pass
        
        # 分析24小时RSI
        if rsi_24h is not None:
            try:
                rsi_24h_val = float(rsi_24h)
                if rsi_24h_val < 30:
                    oversold_24h.append((symbol, rsi_24h_val))
                elif rsi_24h_val > 70:
                    overbought_24h.append((symbol, rsi_24h_val))
                else:
                    neutral_24h.append((symbol, rsi_24h_val))
            except (ValueError, TypeError):
                pass
    
    # 输出1小时RSI分析
    print(f"\n   ⏰ 1小时RSI分析:")
    print(f"      🔴 超卖 (RSI < 30): {len(oversold_1h)} 个币种")
    print(f"      🟡 中性 (30 ≤ RSI ≤ 70): {len(neutral_1h)} 个币种")
    print(f"      🔵 超买 (RSI > 70): {len(overbought_1h)} 个币种")
    
    # 显示最超卖的币种
    if oversold_1h:
        oversold_1h.sort(key=lambda x: x[1])  # 按RSI值排序
        print(f"      📉 最超卖的3个币种:")
        for i, (symbol, rsi) in enumerate(oversold_1h[:3]):
            print(f"         {i+1}. {symbol}: RSI = {rsi:.2f}")
    
    # 显示最超买的币种
    if overbought_1h:
        overbought_1h.sort(key=lambda x: x[1], reverse=True)  # 按RSI值倒序排序
        print(f"      📈 最超买的3个币种:")
        for i, (symbol, rsi) in enumerate(overbought_1h[:3]):
            print(f"         {i+1}. {symbol}: RSI = {rsi:.2f}")
    
    # 输出24小时RSI分析
    print(f"\n   📅 24小时RSI分析:")
    print(f"      🔴 超卖 (RSI < 30): {len(oversold_24h)} 个币种")
    print(f"      🟡 中性 (30 ≤ RSI ≤ 70): {len(neutral_24h)} 个币种")
    print(f"      🔵 超买 (RSI > 70): {len(overbought_24h)} 个币种")

def example_data_management():
    """数据管理示例"""
    print("\n🗂️  数据管理示例")
    print("-" * 40)
    
    orderbook_requester = BinanceOrderbookRequester()
    rsi_requester = CoinglassRSIListRequester()
    
    # 检查数据新鲜度
    print("\n1. 检查数据新鲜度")
    
    # 检查订单薄数据
    is_fresh = orderbook_requester.is_orderbook_data_fresh("BTCUSDT", "futures", 300)  # 5分钟
    print(f"   BTCUSDT合约订单薄: {'✅ 新鲜' if is_fresh else '❌ 过期'}")
    
    # 检查RSI数据
    is_fresh = rsi_requester.file_manager.is_data_fresh(rsi_requester.api_identifier, 600)  # 10分钟
    print(f"   RSI数据: {'✅ 新鲜' if is_fresh else '❌ 过期'}")
    
    # 加载历史数据
    print("\n2. 加载历史数据示例")
    data = orderbook_requester.get_latest_orderbook_data("BTCUSDT", "futures")
    if data:
        last_update_id = data.get('lastUpdateId')
        print(f"   ✅ 成功加载BTCUSDT订单薄数据 (更新ID: {last_update_id})")
    else:
        print("   ❌ 无可用的历史数据")

def main():
    """主函数"""
    print("🚀 订单薄和RSI数据模块使用示例")
    print("=" * 50)
    
    try:
        # 运行各个示例
        example_orderbook_usage()
        example_rsi_usage()
        example_data_management()
        
        print("\n" + "=" * 50)
        print("🎉 示例运行完成！")
        print("\n💡 提示:")
        print("   - 订单薄数据实时性较强，建议频繁更新")
        print("   - RSI数据更新频率较低，可以缓存使用")
        print("   - 使用批量获取可以提高效率")
        print("   - 注意API调用频率限制")
        
    except Exception as e:
        print(f"❌ 示例运行失败: {e}")
        return 1
    
    return 0

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
