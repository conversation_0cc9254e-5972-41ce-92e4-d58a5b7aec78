# 新增模块说明文档

## 概述

本次更新为 `api_requests` 项目新增了两个重要的数据获取模块：

1. **币安订单薄数据管理模块** - 获取币安现货和合约的订单薄深度数据
2. **CoinGlass RSI数据获取模块** - 获取CoinGlass提供的RSI技术指标数据（已优化）

## 新增模块详情

### 1. 币安订单薄数据管理模块

**文件位置**: `api_requests/binance/binance_orderbook_requester.py`

#### 功能特性
- ✅ 支持币安合约订单薄数据获取
- ✅ 支持币安现货订单薄数据获取  
- ✅ 支持批量获取多个交易对
- ✅ 支持多种深度限制 (5, 10, 20, 50, 100, 500, 1000, 5000)
- ✅ 自动数据验证和错误处理
- ✅ 数据新鲜度检查
- ✅ 完整的元数据记录

#### 主要方法
```python
from api_requests.binance.binance_orderbook_requester import BinanceOrderbookRequester

requester = BinanceOrderbookRequester()

# 获取合约订单薄
success = requester.fetch_futures_orderbook("BTCUSDT", 100)

# 获取现货订单薄  
success = requester.fetch_spot_orderbook("BTCUSDT", 100)

# 批量获取
symbols = ["BTCUSDT", "ETHUSDT", "BNBUSDT"]
results = requester.fetch_multiple_orderbooks(symbols, "futures", 50)

# 获取缓存数据
data = requester.get_latest_orderbook_data("BTCUSDT", "futures")

# 检查数据新鲜度
is_fresh = requester.is_orderbook_data_fresh("BTCUSDT", "futures", 60)
```

#### 支持的深度限制
- **合约市场**: 5, 10, 20, 50, 100, 500, 1000
- **现货市场**: 5, 10, 20, 50, 100, 500, 1000, 5000

### 2. CoinGlass RSI数据获取模块

**文件位置**: `api_requests/coinglass/coinglass_get_rsi_list.py`

#### 功能特性
- ✅ 获取多种加密货币的RSI指标
- ✅ 支持多个时间周期 (15m, 1h, 4h, 12h, 24h, 1w)
- ✅ 自动API认证和错误处理
- ✅ 数据验证和统计信息
- ✅ 完整的元数据记录

#### 主要方法
```python
from api_requests.coinglass.coinglass_get_rsi_list import CoinglassRSIListRequester

requester = CoinglassRSIListRequester()

# 获取RSI数据
success = requester.get_rsi_data()

# 加载缓存数据
success, data, message = requester.file_manager.load_latest_api_data(requester.api_identifier)
```

## 配置更新

### BINANCE_CONFIG 新增配置
```python
BINANCE_CONFIG = {
    # ... 现有配置 ...
    "endpoints": {
        # ... 现有端点 ...
        "futures_orderbook": "/fapi/v1/depth",
        "spot_orderbook": "/api/v3/depth"
    },
    "orderbook_limits": {
        "futures": [5, 10, 20, 50, 100, 500, 1000],
        "spot": [5, 10, 20, 50, 100, 500, 1000, 5000]
    }
}
```

## 测试和示例

### 测试脚本
运行完整测试：
```bash
python api_requests/test_orderbook_rsi.py
```

### 使用示例
查看详细使用示例：
```bash
python api_requests/example_usage.py
```

## 数据结构

### 订单薄数据结构
```json
{
  "lastUpdateId": 1234567890,
  "E": 1234567890123,
  "T": 1234567890123,
  "bids": [
    ["价格", "数量"],
    ["50000.00", "1.5"]
  ],
  "asks": [
    ["价格", "数量"], 
    ["50001.00", "2.0"]
  ]
}
```

### RSI数据结构
```json
{
  "code": "0",
  "msg": "success",
  "data": [
    {
      "symbol": "BTCUSDT",
      "rsi15m": "45.67",
      "rsi1h": "52.34", 
      "rsi4h": "48.91",
      "rsi12h": "55.23",
      "rsi24h": "49.87",
      "rsi1w": "62.45"
    }
  ]
}
```

## 使用建议

### 订单薄数据
- **更新频率**: 建议每1-5分钟更新一次
- **深度选择**: 
  - 快速查看: 使用 limit=20
  - 详细分析: 使用 limit=100-500
  - 深度分析: 使用 limit=1000+
- **批量获取**: 一次获取多个交易对可提高效率

### RSI数据  
- **更新频率**: 建议每5-10分钟更新一次
- **缓存策略**: RSI数据变化较慢，可以适当缓存
- **分析应用**: 
  - RSI < 30: 可能超卖
  - RSI > 70: 可能超买
  - 30 ≤ RSI ≤ 70: 中性区间

## 注意事项

1. **API限制**: 注意币安API的调用频率限制
2. **网络异常**: 建议添加重试机制
3. **数据验证**: 使用前检查数据完整性
4. **缓存管理**: 定期清理过期的缓存数据
5. **错误处理**: 监控API返回的错误信息

## 文件结构

```
api_requests/
├── binance/
│   ├── __init__.py                          # 更新了导入
│   ├── binance_orderbook_requester.py       # 新增：订单薄数据请求器
│   └── ...
├── coinglass/
│   ├── __init__.py                          # 更新了导入
│   ├── coinglass_get_rsi_list.py           # 已存在：RSI数据请求器
│   └── ...
├── __init__.py                              # 更新了导入
├── test_orderbook_rsi.py                    # 新增：测试脚本
├── example_usage.py                         # 新增：使用示例
└── README_NEW_MODULES.md                    # 新增：本文档
```

## 更新日志

- ✅ 新增币安订单薄数据请求器
- ✅ 优化CoinGlass RSI数据请求器
- ✅ 更新配置文件支持新功能
- ✅ 更新模块导入结构
- ✅ 添加完整的测试脚本
- ✅ 添加详细的使用示例
- ✅ 添加文档说明
