#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
币安订单薄数据持续监控启动脚本
"""

import sys
import os
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))
sys.path.insert(0, str(project_root / "api_requests"))

def show_config_info():
    """显示当前配置信息"""
    try:
        from config import BINANCE_ORDERBOOK_CONFIG

        print("📊 当前配置信息:")
        print(f"  🔄 更新间隔: {BINANCE_ORDERBOOK_CONFIG.get('update_interval_seconds', 300)} 秒")
        print(f"  🌐 获取模式: {BINANCE_ORDERBOOK_CONFIG.get('fetch_mode', 'manual')}")

        if BINANCE_ORDERBOOK_CONFIG.get('fetch_mode') == 'all_active':
            print(f"  📈 最大合约数量: {BINANCE_ORDERBOOK_CONFIG.get('max_futures_symbols', 150)}")
            print(f"  📉 最大现货数量: {BINANCE_ORDERBOOK_CONFIG.get('max_spot_symbols', 100)}")
            print(f"  📦 合约批次大小: {BINANCE_ORDERBOOK_CONFIG.get('futures_batch_size', 30)}")
            print(f"  📦 现货批次大小: {BINANCE_ORDERBOOK_CONFIG.get('spot_batch_size', 20)}")
        else:
            print(f"  📈 合约交易对: {len(BINANCE_ORDERBOOK_CONFIG.get('symbols', {}).get('futures', []))} 个")
            print(f"  📉 现货交易对: {len(BINANCE_ORDERBOOK_CONFIG.get('symbols', {}).get('spot', []))} 个")

        print(f"  📊 订单簿深度: {BINANCE_ORDERBOOK_CONFIG.get('orderbook_limit', 50)}")
        print(f"  ⏱️ 批次间延迟: {BINANCE_ORDERBOOK_CONFIG.get('delay_between_batches', 2)} 秒")

    except Exception as e:
        print(f"❌ 读取配置失败: {e}")

def main():
    """主程序入口"""
    try:
        print("🚀 启动币安订单薄数据监控器...")
        print("📁 项目路径:", project_root)
        print("=" * 60)

        # 显示配置信息
        show_config_info()
        print("=" * 60)

        # 导入并启动监控器
        from api_requests.binance.binance_orderbook_requester import BinanceOrderbookRequester

        # 创建请求器实例
        requester = BinanceOrderbookRequester()

        print("✅ 监控器初始化完成")
        print("💡 按 Ctrl+C 停止监控")
        print("📊 配置文件: config.py")
        print("=" * 60)

        # 启动持续监控
        requester.start_continuous_monitoring()
        
    except KeyboardInterrupt:
        print("\n🛑 用户中断，监控器已停止")
    except ImportError as e:
        print(f"❌ 导入错误: {e}")
        print("💡 请确保在项目根目录运行此脚本")
        sys.exit(1)
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
