#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
币安订单薄数据持续监控启动脚本
"""

import sys
import os
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))
sys.path.insert(0, str(project_root / "api_requests"))

def main():
    """主程序入口"""
    try:
        print("🚀 启动币安订单薄数据监控器...")
        print("📁 项目路径:", project_root)
        
        # 导入并启动监控器
        from api_requests.binance.binance_orderbook_requester import BinanceOrderbookRequester
        
        # 创建请求器实例
        requester = BinanceOrderbookRequester()
        
        print("✅ 监控器初始化完成")
        print("💡 按 Ctrl+C 停止监控")
        print("📊 配置文件: config.py")
        print("=" * 50)
        
        # 启动持续监控
        requester.start_continuous_monitoring()
        
    except KeyboardInterrupt:
        print("\n🛑 用户中断，监控器已停止")
    except ImportError as e:
        print(f"❌ 导入错误: {e}")
        print("💡 请确保在项目根目录运行此脚本")
        sys.exit(1)
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
