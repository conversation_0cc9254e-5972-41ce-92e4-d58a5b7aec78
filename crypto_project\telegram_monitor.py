#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Telegram消息监听器模块 - 大额转账监控
从 crypto_trading_bot.py 移植而来
"""

import os
import asyncio
import logging
import requests
import time
import random
import json
import hashlib
import re
import threading
from datetime import datetime, timezone, timedelta
from typing import Dict, List, Any, Optional, Tuple

# 配置日志
logger = logging.getLogger(__name__)

# 检查 Telethon 是否可用
try:
    from telethon import TelegramClient, events
    TELETHON_AVAILABLE = True
except ImportError:
    TELETHON_AVAILABLE = False
    logger.warning("⚠️ Telethon未安装，Telegram监听功能将不可用")

class TelegramMonitor:
    """Telegram消息监听器 - 大额转账监控"""
    
    # ==================== 频道白名单配置 ====================
    # 直接在这里修改配置，无需编辑其他文件
    
    # 🔧 基本配置 - 已禁用白名单过滤，直接处理所有频道消息
    FILTER_ENABLED = False                   # True=启用过滤功能，False=关闭过滤(所有频道都允许)
    FILTER_MODE = "disabled"                # "whitelist"=白名单模式(只允许指定频道)，"blacklist"=黑名单模式(屏蔽指定频道)，"disabled"=禁用过滤
    
    # 📋 白名单频道列表（只有这些频道的消息会被处理）
    ALLOWED_CHANNELS = [
        {
            "id": 7134479864,
            "name": "未知来源",
            "username": "",
            "description": "用户指定监控频道",
            "enabled": True
        }
    ]
    
    # 🚫 黑名单频道列表（这些频道的消息会被屏蔽）
    BLOCKED_CHANNELS = []
    
    # ⚙️ 高级配置
    AUTO_ADD_CHANNELS = False               # 是否自动添加新频道到白名单
    REQUIRE_KEYWORDS = []                   # 必须包含的关键词列表
    
    # ==================== 配置结束 ====================
    
    # 消息过滤规则 - 黑名单模式
    BLACKLIST_PATTERNS = [
        r'transferred from unknown wallet to unknown wallet',
        r'transferred from unknown wallet to unknown new wallet'
    ]

    # 替换规则 - 保持原有符号类型和数量
    REPLACEMENT_PATTERNS = [
        # 处理unknown wallet显示
        (r'unknown wallet', r'未知钱包'),
        (r'unknown new wallet', r'新的未知钱包'),
        
        # 处理基本转账信息格式化（保留警报灯和#号）
        (r'((?:🚨\s*)*)?(\d[\d,]*)\s+(#?\w+)\s+\(([0-9,]+)\s+USD\)\s+transferred\s+from\s+(.+?)\s+to\s+(.+?)(?=\n/$)', 
         r'\1\n🐋 \3 大额转账\n💰 数量：\2 \3\n💵 价值：$\4\n📤 从：\5\n📥 到：\6'),
        
        # 处理minted消息格式化（保留警报灯和#号）
        (r'((?:💵\s*)*)?(\d[\d,]*)\s+(#?\w+)\s+\(([0-9,]+)\s+USD\)\s+minted\s+at\s+(.+?)(?=\n/$)', 
         r'\1\n🏭 \3 铸造\n💰 数量：\2 \3\n💵 价值：$\4\n🏢 铸造方：\5'),
        
        # 处理burned消息格式化（保留警报灯和#号）
        (r'((?:🔥\s*)*)?(\d[\d,]*)\s+(#?\w+)\s+\(([0-9,]+)\s+USD\)\s+burned\s+at\s+(.+?)(?=\n/$)', 
         r'\1\n🔥 \3 销毁\n💰 数量：\2 \3\n💵 价值：$\4\n🏢 销毁方：\5'),
        
        # 处理手续费消息格式化（保留警报灯和#号）
        (r'((?:💸\s*)*)?A fee of (\d[\d,]*)\s+(#?\w+)\s+\(([0-9,]+)\s+USD\)\s+has just been paid for a single transaction!(?=\n/$)', 
         r'\1\n💸 \3 高额手续费\n💰 数量：\2 \3\n💵 价值：$\4\n🔗 单笔交易手续费'),
        
        # 处理解锁消息格式化（保留警报灯和#号）
        (r'((?:🔓\s*)*)?(\d[\d,]*)\s+(#?\w+)\s+\(([0-9,]+)\s+USD\)\s+unlocked from escrow at\s+(.+?)(?=\n/$)', 
         r'\1\n🔓 \3 解锁\n💰 数量：\2 \3\n💵 价值：$\4\n🏢 解锁方：\5'),
        
        # 处理休眠地址激活消息格式化（保留警报灯和#号）- 支持各种类型的休眠地址
        (r'((?:💤\s*)*)?A dormant(?:\s+[\w-]+)?\s+address containing (\d[\d,]*)\s+(#?\w+)\s+\(([0-9,]+)\s+USD\)\s+has just been activated after\s+([^(!)]+?)(?:\s*\([^)]*\))?!?(?=\n/$)', 
         r'💤 \3 休眠地址激活\n💰 数量：\2 \3\n💵 当前价值：$\4\n⏰ 休眠时间：\5'),
        
        # 处理冻结地址消息格式化（保留警报灯和#号）
        (r'((?:❄️\s*)*)?An address with a balance of (\d[\d,]*)\s+(#?\w+)\s+\(([0-9,]+)\s+USD\)\s+has just been frozen!(?=\n/$)', 
         r'\1\n🦾 \3 地址冻结\n💰 数量：\2 \3\n💵 价值：$\4\n🚫 状态：地址已被冻结'),
        
        # 强制替换Details为超链接格式
        (r'Details \(([^)]+)\)', r'🔗 <a href="\1">查看详情</a>'),
        (r'🔗 查看详情 \(([^)]+)\)', r'🔗 <a href="\1">查看详情</a>'),
        (r'Details', r'🔗 查看详情'),
        
        # 时间单位中文化
        (r'(\d+(?:\.\d+)?)\s+years?', r'\1年'),
        (r'(\d+(?:\.\d+)?)\s+months?', r'\1个月'),
        (r'(\d+(?:\.\d+)?)\s+weeks?', r'\1周'),
        (r'(\d+(?:\.\d+)?)\s+days?', r'\1天'),
        (r'(\d+(?:\.\d+)?)\s+hours?', r'\1小时'),
        (r'(\d+(?:\.\d+)?)\s+minutes?', r'\1分钟'),
        (r'(\d+(?:\.\d+)?)\s+seconds?', r'\1秒')
    ]

    def __init__(self, subscription_manager, telegram_config=None):
        """
        初始化 TelegramMonitor
        
        Args:
            subscription_manager: 订阅管理器实例
            telegram_config: Telegram配置字典，包含API_ID, API_HASH, PHONE, PASSWORD等
        """
        self.subscription_manager = subscription_manager
        self.client = None
        self.session = None
        self.running = False
        self.cache_duration = 600  # 10分钟缓存
        self.transfer_cache = {}
        
        # Telegram配置
        self.telegram_config = telegram_config or {}
        
        # 频道管理配置
        self.channel_config = self._load_channel_config()
        
        # 预编译正则表达式以提高性能
        self.blacklist_patterns = [re.compile(pattern, re.MULTILINE | re.DOTALL) for pattern in self.BLACKLIST_PATTERNS]
        self.replacement_patterns = [(re.compile(pattern, re.MULTILINE | re.DOTALL), replacement) 
                                   for pattern, replacement in self.REPLACEMENT_PATTERNS]
        
        # 频道特定规则配置
        self.channel_specific_rules = {
            "whale_alert": {
                "match_patterns": ["whale", "alert", "🚨"],
                "blacklist_patterns": [
                    r'transferred from unknown wallet to unknown wallet',
                    r'transferred from unknown wallet to unknown new wallet'
                ],
                "whitelist_patterns": [
                    r'transferred',
                    r'minted',
                    r'burned',
                    r'fee.*paid',
                    r'unlocked',
                    r'dormant.*activated',
                    r'frozen'
                ]
            }
        }
        
        # 预编译频道特定规则
        self._channel_rules = {}
        for rule_name, rule_config in self.channel_specific_rules.items():
            compiled_rule = {
                'match_patterns': [pattern.lower() for pattern in rule_config.get('match_patterns', [])],
                'blacklist_patterns': [re.compile(pattern, re.MULTILINE | re.DOTALL) 
                                     for pattern in rule_config.get('blacklist_patterns', [])],
                'whitelist_patterns': [re.compile(pattern, re.MULTILINE | re.DOTALL) 
                                     for pattern in rule_config.get('whitelist_patterns', [])]
            }
            self._channel_rules[rule_name] = compiled_rule
        
        # 统计信息
        self.stats = {
            'received': 0,
            'processed': 0,
            'filtered': 0,
            'blacklisted': 0,
            'whitelisted': 0,
            'sent_success': 0,
            'sent_failed': 0,
            'channel_filtered': 0,
            'start_time': time.time()
        }

    async def init_client(self):
        """初始化Telegram客户端，使用持久化session"""
        if not TELETHON_AVAILABLE:
            logger.error("❌ Telethon未安装，无法启动Telegram监听器")
            return False

        # 从配置中获取必要参数
        api_id = self.telegram_config.get('API_ID')
        api_hash = self.telegram_config.get('API_HASH')
        phone = self.telegram_config.get('PHONE')
        password = self.telegram_config.get('PASSWORD')
        session_file = self.telegram_config.get('SESSION_FILE', 'telegram_session.session')

        if not all([api_id, api_hash, phone]):
            logger.error("❌ Telegram配置不完整，缺少API_ID、API_HASH或PHONE")
            return False

        max_retries = 3

        for attempt in range(max_retries):
            try:
                logger.info(f"🔄 尝试初始化Telegram客户端 (第{attempt + 1}次)")

                # 检查持久化session文件状态
                session_base_name = session_file.replace('.session', '')

                if os.path.exists(session_file):
                    logger.info(f"📁 发现现有session文件: {session_file}")
                    try:
                        # 验证session文件完整性
                        file_size = os.path.getsize(session_file)
                        if file_size < 100:  # session文件太小可能损坏
                            logger.warning(f"⚠️ Session文件异常小 ({file_size} bytes)，将重新创建")
                            os.remove(session_file)
                        else:
                            logger.info(f"✅ Session文件完整: {file_size} bytes")
                    except Exception as e:
                        logger.warning(f"⚠️ 检查session文件时出错: {e}")
                        # 如果检查失败，删除可能损坏的文件
                        try:
                            os.remove(session_file)
                        except:
                            pass
                else:
                    logger.info(f"🆕 将创建新的session文件: {session_file}")

                # 创建客户端实例，使用固定的session名称
                self.client = TelegramClient(
                    session_base_name,  # Telethon会自动添加.session后缀
                    api_id,
                    api_hash,
                    device_model="Signal Monitor Bot",
                    system_version="2.0",
                    app_version="2.0",
                    lang_code="zh-CN",
                    timeout=30
                )

                # 设置连接超时和flood防护
                self.client.session.flood_sleep_threshold = 60

                # 连接并登录
                logger.info("🔗 正在连接到Telegram...")
                await self.client.start(phone=phone, password=password)

                if not await self.client.is_user_authorized():
                    logger.error("❌ Telegram客户端未授权")
                    await self.client.disconnect()
                    continue

                # 验证session文件是否正常创建
                if os.path.exists(session_file):
                    file_size = os.path.getsize(session_file)
                    logger.info(f"✅ Session文件已保存: {session_file} ({file_size} bytes)")

                logger.info("✅ Telegram客户端初始化成功，session已持久化")
                return True

            except Exception as e:
                logger.error(f"❌ Telegram客户端初始化失败 (第{attempt + 1}次): {e}")

                # 尝试断开连接
                try:
                    if hasattr(self, 'client') and self.client:
                        await self.client.disconnect()
                except:
                    pass

                if attempt < max_retries - 1:
                    wait_time = (attempt + 1) * 5
                    logger.info(f"⏳ 等待 {wait_time} 秒后重试...")
                    await asyncio.sleep(wait_time)
                else:
                    logger.error("❌ Telegram客户端初始化失败")
                    return False

        return False

    async def init_session(self):
        """初始化会话"""
        try:
            if not self.client.is_connected():
                await self.client.connect()
        except Exception as e:
            logger.error(f"会话初始化失败: {e}")

    async def close_session(self):
        """关闭会话"""
        try:
            if self.client and self.client.is_connected():
                await self.client.disconnect()
        except Exception as e:
            logger.error(f"关闭会话失败: {e}")

    def _load_channel_config(self) -> dict:
        """加载频道配置 - 使用类变量配置"""
        import time

        # 为频道添加时间戳（如果没有的话）
        allowed_channels = []
        for channel in self.ALLOWED_CHANNELS:
            channel_copy = channel.copy()
            if 'added_at' not in channel_copy:
                channel_copy['added_at'] = time.time()
            # 只添加启用的频道
            if channel_copy.get('enabled', True):
                allowed_channels.append(channel_copy)

        blocked_channels = []
        for channel in self.BLOCKED_CHANNELS:
            channel_copy = channel.copy()
            if 'added_at' not in channel_copy:
                channel_copy['added_at'] = time.time()
            # 只添加启用的频道
            if channel_copy.get('enabled', True):
                blocked_channels.append(channel_copy)

        # 构建配置对象
        config = {
            "enabled": self.FILTER_ENABLED,
            "mode": self.FILTER_MODE,
            "allowed_channels": allowed_channels,
            "blocked_channels": blocked_channels,
            "channel_rules": {},
            "auto_add_channels": self.AUTO_ADD_CHANNELS,
            "require_keywords": self.REQUIRE_KEYWORDS,
            "updated_at": time.time()
        }

        # 打印配置状态
        logger.info("🔧 加载频道白名单配置...")
        if self.FILTER_ENABLED:
            if self.FILTER_MODE == "whitelist":
                logger.info(f"✅ 频道白名单模式启用: {len(allowed_channels)} 个允许频道")
                for channel in allowed_channels:
                    status = "🟢" if channel.get('enabled', True) else "🔴"
                    logger.info(f"   📝 {status} 允许频道: {channel['name']} (ID: {channel['id']})")
            else:
                logger.info(f"✅ 频道黑名单模式启用: {len(blocked_channels)} 个屏蔽频道")
                for channel in blocked_channels:
                    status = "🟢" if channel.get('enabled', True) else "🔴"
                    logger.info(f"   🚫 {status} 屏蔽频道: {channel['name']} (ID: {channel['id']})")
        else:
            logger.info("🔓 频道过滤功能已关闭，所有频道都允许")

        logger.info(f"⚙️ 自动添加频道: {'启用' if self.AUTO_ADD_CHANNELS else '禁用'}")
        if self.REQUIRE_KEYWORDS:
            logger.info(f"🔍 必须关键词: {', '.join(self.REQUIRE_KEYWORDS)}")

        return config

    def _save_channel_config(self, config: dict = None):
        """保存频道配置 - 硬编码模式下不保存到文件"""
        # 硬编码配置模式下，不需要保存到文件
        # 所有配置都在 _load_channel_config() 方法中硬编码
        logger.debug("🔧 硬编码配置模式，跳过文件保存")
        pass

    def is_channel_allowed(self, channel_id: int, channel_name: str) -> bool:
        """检查频道是否被允许"""
        try:
            # 获取配置
            enabled = self.channel_config.get('enabled', True)
            mode = self.channel_config.get('mode', 'blacklist')
            allowed_channels = self.channel_config.get('allowed_channels', [])
            blocked_channels = self.channel_config.get('blocked_channels', [])

            # 详细日志输出当前配置状态
            logger.info(f"🔍 频道权限检查: {channel_name} (ID: {channel_id})")
            logger.info(f"   📋 过滤功能启用: {enabled}")
            logger.info(f"   📋 过滤模式: {mode}")
            logger.info(f"   📋 白名单频道数量: {len(allowed_channels)}")

            # 如果功能未启用，允许所有频道
            if not enabled:
                logger.info(f"✅ 过滤功能已禁用，允许所有频道: {channel_name}")
                return True

            # 首先检查是否在屏蔽列表中
            if self._is_channel_in_list(channel_id, channel_name, blocked_channels):
                logger.warning(f"🚫 频道在黑名单中，拒绝: {channel_name} (ID: {channel_id})")
                self.stats['channel_filtered'] += 1
                return False

            if mode == "whitelist":
                # 白名单模式：只允许列表中的频道
                logger.info(f"🔍 白名单模式检查: {channel_name} (ID: {channel_id})")

                # 详细检查每个白名单项
                for i, allowed_channel in enumerate(allowed_channels):
                    logger.info(f"   📋 检查白名单项 {i+1}: {allowed_channel}")

                allowed = self._is_channel_in_list(channel_id, channel_name, allowed_channels)

                if allowed:
                    logger.info(f"✅ 频道在白名单中，允许: {channel_name} (ID: {channel_id})")
                    return True
                else:
                    logger.warning(f"🚫 频道不在白名单中，拒绝: {channel_name} (ID: {channel_id})")
                    logger.info(f"   📋 当前白名单:")
                    for i, ch in enumerate(allowed_channels):
                        logger.info(f"     {i+1}. {ch.get('name', 'Unknown')} (ID: {ch.get('id', 'Unknown')})")

                    self.stats['channel_filtered'] += 1

                    # 如果启用自动添加，询问是否添加
                    if self.channel_config.get('auto_add_channels', False):
                        logger.info(f"🔄 自动添加频道到白名单: {channel_name}")
                        self.add_channel_to_allowed(channel_id, channel_name)
                        return True

                    return False
            else:
                # 黑名单模式：屏蔽列表之外的都允许
                logger.info(f"✅ 黑名单模式，频道不在黑名单中，允许: {channel_name}")
                return True

        except Exception as e:
            logger.error(f"❌ 检查频道权限失败: {e}")
            import traceback
            logger.error(f"详细错误: {traceback.format_exc()}")
            return True  # 出错时默认允许

    def _is_channel_in_list(self, channel_id: int, channel_name: str, channel_list: list) -> bool:
        """检查频道是否在指定列表中"""
        logger.info(f"🔍 检查频道是否在列表中: {channel_name} (ID: {channel_id})")
        logger.info(f"   📋 列表长度: {len(channel_list)}")

        for i, item in enumerate(channel_list):
            logger.info(f"   📋 检查列表项 {i+1}: {item} (类型: {type(item).__name__})")

            if isinstance(item, int):
                logger.info(f"     🔢 整数匹配检查: {item} == {channel_id} ? {item == channel_id}")
                if item == channel_id:
                    logger.info(f"✅ 通过整数ID匹配找到频道")
                    return True
            elif isinstance(item, str):
                # 支持名称匹配（不区分大小写）
                match_result = item.lower() in channel_name.lower()
                logger.info(f"     📝 字符串匹配检查: '{item.lower()}' in '{channel_name.lower()}' ? {match_result}")
                if match_result:
                    logger.info(f"✅ 通过字符串名称匹配找到频道")
                    return True
            elif isinstance(item, dict):
                # 支持详细配置
                item_id = item.get('id')
                item_name = item.get('name', '')
                id_match = item_id == channel_id
                name_match = item_name.lower() in channel_name.lower() if item_name else False
                logger.info(f"     📋 字典匹配检查:")
                logger.info(f"       🆔 ID匹配: {item_id} == {channel_id} ? {id_match}")
                logger.info(f"       📝 名称匹配: '{item_name.lower()}' in '{channel_name.lower()}' ? {name_match}")

                if id_match or name_match:
                    logger.info(f"✅ 通过字典配置匹配找到频道")
                    return True

        logger.info(f"🚫 频道不在列表中")
        return False

    def add_channel_to_allowed(self, channel_id: int, channel_name: str):
        """添加频道到允许列表"""
        try:
            allowed_channels = self.channel_config.get('allowed_channels', [])

            # 检查是否已存在
            if self._is_channel_in_list(channel_id, channel_name, allowed_channels):
                return False

            # 添加新频道
            channel_info = {
                'id': channel_id,
                'name': channel_name,
                'added_at': time.time(),
                'auto_added': True
            }

            allowed_channels.append(channel_info)
            self.channel_config['allowed_channels'] = allowed_channels
            self._save_channel_config()

            logger.info(f"✅ 频道已添加到允许列表: {channel_name} (ID: {channel_id})")
            return True

        except Exception as e:
            logger.error(f"❌ 添加频道到允许列表失败: {e}")
            return False

    def _get_channel_rules(self, channel_name: str) -> dict:
        """根据频道名称获取适用的规则"""
        channel_name_lower = channel_name.lower()

        for rule_name, rule_config in self._channel_rules.items():
            for match_pattern in rule_config['match_patterns']:
                if match_pattern in channel_name_lower:
                    logger.debug(f"频道 '{channel_name}' 匹配规则: {rule_name}")
                    return rule_config

        # 如果没有匹配的特定规则，返回空规则
        return {
            'blacklist_patterns': [],
            'whitelist_patterns': []
        }

    def _is_blacklisted(self, text: str, channel_name: str = "") -> bool:
        """检查是否在黑名单中"""
        if not text:
            return False

        # 获取频道特定规则
        channel_rules = self._get_channel_rules(channel_name)

        # 检查全局黑名单
        for pattern in self.blacklist_patterns:
            if pattern.search(text):
                self.stats['blacklisted'] += 1
                logger.debug(f"消息被全局黑名单过滤: {channel_name}")
                return True

        # 检查频道特定黑名单
        for pattern in channel_rules.get('blacklist_patterns', []):
            if pattern.search(text):
                self.stats['blacklisted'] += 1
                logger.debug(f"消息被频道特定黑名单过滤: {channel_name}")
                return True

        return False

    def _is_whitelisted(self, text: str, channel_name: str = "") -> bool:
        """检查是否通过白名单验证"""
        if not text:
            return True

        # 获取频道特定规则
        channel_rules = self._get_channel_rules(channel_name)

        # 获取白名单模式
        whitelist_patterns = channel_rules.get('whitelist_patterns', [])

        # 如果没有配置任何白名单，默认通过
        if not whitelist_patterns:
            return True

        # 检查是否匹配任何白名单规则
        for pattern in whitelist_patterns:
            if pattern.search(text):
                logger.debug(f"消息通过白名单验证: {channel_name}")
                return True

        # 如果有白名单但都不匹配，则被过滤
        self.stats['whitelisted'] += 1
        logger.debug(f"消息未通过白名单验证: {channel_name}")
        return False

    def _apply_replacement_rules(self, text: str, channel_name: str = "") -> str:
        """应用替换规则"""
        if not text:
            return text

        result = text
        replacements = 0

        # 应用全局替换规则
        for pattern, replacement in self.replacement_patterns:
            before_text = result
            result = pattern.sub(replacement, result)

            if result != before_text:
                replacements += 1
                logger.debug(f"应用替换规则: {pattern.pattern[:50]}...")

        # 后处理：如果没有警报符号开头，添加默认符号
        result = self._add_default_symbols_if_needed(result)

        if replacements > 0:
            logger.debug(f"应用了 {replacements} 条替换规则")

        return result

    def _add_default_symbols_if_needed(self, text: str) -> str:
        """如果格式化后的文本没有符号开头，添加默认符号"""
        if not text:
            return text

        lines = text.strip().split('\n')
        if not lines:
            return text

        first_line = lines[0].strip()

        # 检查第一行是否已经有符号
        emoji_pattern = re.compile(r'^[\U0001F300-\U0001F9FF\U0001F600-\U0001F64F\U0001F680-\U0001F6FF\U00002600-\U00002B55]+')
        if emoji_pattern.match(first_line):
            return text  # 已经有符号，不需要添加

        # 根据内容类型添加默认符号
        if '大额转账' in text:
            return '🚨 ' + text
        elif '铸造' in text:
            return '💵 ' + text
        elif '销毁' in text or '燃烧' in text:
            return '🔥 ' + text
        elif '解锁' in text:
            return '🔓 ' + text
        elif '冻结' in text:
            return '🦾 ' + text
        elif '休眠' in text:
            return '💤 ' + text
        elif '手续费' in text:
            return '💸 ' + text
        else:
            return '🚨 ' + text  # 默认使用警报符号

    def _format_for_telegram(self, text: str) -> str:
        """格式化为Telegram支持的HTML"""
        if not text:
            return ""

        # 转换Markdown格式为HTML
        # **粗体** -> <b>粗体</b>
        text = re.sub(r'\*\*([^*]+?)\*\*', r'<b>\1</b>', text)
        # *斜体* -> <i>斜体</i>
        text = re.sub(r'(?<!\*)\*([^*]+?)\*(?!\*)', r'<i>\1</i>', text)
        # __下划线__ -> <u>下划线</u>
        text = re.sub(r'__([^_]+?)__', r'<u>\1</u>', text)

        # 转换Markdown链接为HTML
        link_pattern = re.compile(r'\[([^\]]+)\]\(([^)]+)\)')
        def replace_link(match):
            link_text = match.group(1)
            url = match.group(2)
            return f'<a href="{url}">{link_text}</a>'

        text = link_pattern.sub(replace_link, text)

        return text

    def _is_duplicate_transfer(self, message_text: str) -> bool:
        """检查是否为重复的转账消息"""
        message_hash = hashlib.md5(message_text.encode()).hexdigest()
        current_time = int(time.time())

        # 检查缓存
        if message_hash in self.transfer_cache:
            last_time = self.transfer_cache[message_hash]
            if current_time - last_time < self.cache_duration:
                return True

        # 更新缓存
        self.transfer_cache[message_hash] = current_time

        # 清理过期缓存
        expired_keys = [k for k, v in self.transfer_cache.items()
                       if current_time - v > self.cache_duration]
        for key in expired_keys:
            del self.transfer_cache[key]

        return False

    async def process_message(self, message, channel_name: str) -> Tuple[bool, str]:
        """处理消息"""
        try:
            self.stats['processed'] += 1

            message_text = message.text or ""

            # 跳过空消息
            if not message_text.strip():
                return False, ""

            # 检查黑名单
            if self._is_blacklisted(message_text, channel_name):
                logger.debug(f"消息被黑名单过滤: {channel_name}")
                return False, ""

            # 消息白名单过滤已禁用 - 直接处理所有消息内容
            logger.debug(f"消息白名单过滤已禁用，直接处理消息: {channel_name}")

            # 检查重复消息
            if self._is_duplicate_transfer(message_text):
                logger.debug(f"重复消息过滤: {channel_name}")
                return False, ""

            # 应用替换规则格式化
            formatted_text = self._apply_replacement_rules(message_text, channel_name)

            # 如果格式化后文本为空，使用原始文本
            if not formatted_text.strip():
                logger.debug(f"格式化后文本为空，使用原始文本: {channel_name}")
                formatted_text = message_text
                self.stats['filtered'] += 1

            # 格式化为Telegram HTML
            final_text = self._format_for_telegram(formatted_text)

            logger.debug(f"消息处理完成 / 来源: {channel_name}")
            return True, final_text

        except Exception as e:
            logger.error(f"❌ 处理消息时出错: {e}")
            return False, ""

    async def send_transfer_alert_to_subscribers(self, message: str):
        """发送转账警报给订阅用户 - 已禁用"""
        # 🔧 大额转账功能已禁用
        logger.info("🚫 大额转账功能已禁用，跳过发送")
        return

    async def message_handler(self, event):
        """消息处理器"""
        try:
            self.stats['received'] += 1

            # 获取基本信息
            chat = await event.get_chat()
            channel_name = getattr(chat, 'title', '未知来源')
            channel_id = getattr(chat, 'id', 0)
            message_text = event.message.text or ""

            # 记录收到的消息
            message_preview = message_text[:100] + ('...' if len(message_text) > 100 else '')
            logger.info(f"📨 收到消息 / 来源: {channel_name} (ID: {channel_id}) / 内容: {message_preview}")

            # 频道白名单过滤已禁用 - 直接处理所有频道的消息
            logger.info(f"✅ 频道白名单过滤已禁用，直接处理消息: {channel_name} (ID: {channel_id})")

            # 跳过空消息
            if not message_text.strip():
                logger.debug(f"🚫 跳过空消息 / 来源: {channel_name}")
                return

            # 处理消息
            logger.debug(f"🔄 开始处理消息 / 来源: {channel_name}")
            success, processed_text = await self.process_message(event.message, channel_name)

            # 如果处理成功且有内容，发送消息
            if success and processed_text:
                active_subscribers = self.subscription_manager.get_active_subscribers()
                if active_subscribers:
                    logger.info(f"📤 发送大额转账警报给 {len(active_subscribers)} 个订阅用户")
                    await self.send_transfer_alert_to_subscribers(processed_text)
                else:
                    logger.info(f"⚠️ 没有活跃订阅用户，跳过发送")
            else:
                logger.debug(f"🚫 消息被过滤或处理失败 / 来源: {channel_name}")

        except Exception as e:
            logger.error(f"❌ 消息处理错误: {e}")
            import traceback
            logger.error(f"详细错误: {traceback.format_exc()}")

    async def run_monitor(self):
        """运行监听器"""
        if not TELETHON_AVAILABLE:
            logger.warning("⚠️ Telethon未可用，跳过Telegram监听")
            return

        try:
            logger.info("📡 Telegram监听器开始运行...")

            # 客户端应该已经在外部初始化了，这里只检查状态
            if not self.client:
                logger.error("❌ Telegram客户端未初始化")
                return

            # 注册消息处理器
            self.client.add_event_handler(self.message_handler, events.NewMessage())
            logger.info("✅ 消息处理器已注册")

            self.running = True
            logger.info("🎯 Telegram监听器正在监听消息...")

            # 保持运行并监听消息
            await self.client.run_until_disconnected()

        except Exception as e:
            logger.error(f"❌ Telegram监听器运行错误: {e}")
            import traceback
            logger.error(f"详细错误: {traceback.format_exc()}")
        finally:
            self.running = False
            logger.info("📡 Telegram监听器已停止")
            if self.client:
                await self.client.disconnect()

    def stop_monitoring(self):
        """停止监听"""
        self.running = False
        if self.client:
            asyncio.create_task(self.client.disconnect())

    def get_stats(self) -> dict:
        """获取统计信息"""
        return self.stats.copy()

    def reset_stats(self):
        """重置统计信息"""
        self.stats = {
            'received': 0,
            'processed': 0,
            'filtered': 0,
            'blacklisted': 0,
            'whitelisted': 0,
            'sent_success': 0,
            'sent_failed': 0,
            'channel_filtered': 0,
            'start_time': time.time()
        }

    def get_channel_config_summary(self) -> dict:
        """获取频道配置摘要"""
        config = self.channel_config
        return {
            'enabled': config.get('enabled', True),
            'mode': config.get('mode', 'blacklist'),
            'allowed_count': len(config.get('allowed_channels', [])),
            'blocked_count': len(config.get('blocked_channels', [])),
            'auto_add': config.get('auto_add_channels', False),
            'stats': {
                'total_received': self.stats['received'],
                'channel_filtered': self.stats['channel_filtered'],
                'filter_rate': round(self.stats['channel_filtered'] / max(self.stats['received'], 1) * 100, 2)
            }
        }
