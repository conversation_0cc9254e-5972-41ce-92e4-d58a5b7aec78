#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
币安订单薄数据请求器
获取币安现货和合约的订单薄深度数据
"""

import sys
from pathlib import Path
from typing import Dict, Any, Optional, List, Tuple
import time

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from main import FileManager, HTTPClient, setup_logger
from config import BINANCE_CONFIG

class BinanceOrderbookRequester:
    """币安订单薄数据请求器"""
    
    def __init__(self):
        """初始化请求器"""
        self.api_identifier = "binance_orderbook"
        self.logger = setup_logger(f"api_requests.{self.api_identifier}")
        self.file_manager = FileManager()
        self.http_client = HTTPClient(
            timeout=BINANCE_CONFIG["timeout"],
            max_retries=BINANCE_CONFIG["max_retries"]
        )
        
        # API配置
        self.futures_base_url = BINANCE_CONFIG["futures_base_url"]
        self.spot_base_url = BINANCE_CONFIG["spot_base_url"]
        self.endpoints = BINANCE_CONFIG["endpoints"]
        self.orderbook_limits = BINANCE_CONFIG["orderbook_limits"]
        
        self.logger.info(f"✅ {self.api_identifier} 请求器初始化完成")
    
    def fetch_futures_orderbook(self, symbol: str, limit: int = 1000) -> bool:
        """
        获取合约订单薄深度数据
        
        Args:
            symbol: 交易对符号，如 BTCUSDT
            limit: 深度限制 (5, 10, 20, 50, 100, 500, 1000)
            
        Returns:
            bool: 是否成功获取数据
        """
        try:
            # 验证limit参数
            if limit not in self.orderbook_limits["futures"]:
                self.logger.error(f"❌ 无效的limit参数: {limit}, 支持的值: {self.orderbook_limits['futures']}")
                return False
            
            self.logger.info(f"🚀 开始获取币安合约订单薄数据: {symbol} (limit: {limit})")
            
            # 构建请求URL
            url = f"{self.futures_base_url}{self.endpoints['futures_orderbook']}"
            
            # 构建请求参数
            params = {
                "symbol": symbol,
                "limit": limit
            }
            
            # 发送请求
            success, response, message = self.http_client.get(
                url=url,
                params=params
            )
            
            if not success or not response:
                self.logger.error(f"❌ 获取合约订单薄数据失败：{message}")
                return False
            
            # 验证响应数据
            if not isinstance(response, dict):
                self.logger.error(f"❌ 响应数据格式错误")
                return False
            
            # 计算统计信息
            bids = response.get('bids', [])
            asks = response.get('asks', [])
            bids_count = len(bids)
            asks_count = len(asks)
            
            # 构建元数据
            metadata = {
                "request_url": url,
                "request_params": params,
                "symbol": symbol,
                "limit": limit,
                "bids_count": bids_count,
                "asks_count": asks_count,
                "data_type": "futures_orderbook_depth",
                "market_type": "futures",
                "last_update_id": response.get('lastUpdateId'),
                "message_output_time": response.get('E')
            }
            
            # 保存数据 - 使用自定义路径保存到 binance_orderbook 文件夹下
            save_success, file_path, save_message = self._save_orderbook_data(
                symbol=symbol,
                market_type="futures",
                data=response,
                metadata=metadata
            )
            
            if save_success:
                self.logger.info(f"✅ 成功获取并保存 {symbol} 合约订单薄数据 (买单:{bids_count}, 卖单:{asks_count})")
                return True
            else:
                self.logger.error(f"❌ 保存数据失败: {save_message}")
                return False
                
        except Exception as e:
            self.logger.error(f"❌ 获取币安合约订单薄数据失败: {e}")
            return False
    
    def fetch_spot_orderbook(self, symbol: str, limit: int = 1000) -> bool:
        """
        获取现货订单薄深度数据
        
        Args:
            symbol: 交易对符号，如 BTCUSDT
            limit: 深度限制 (5, 10, 20, 50, 100, 500, 1000, 5000)
            
        Returns:
            bool: 是否成功获取数据
        """
        try:
            # 验证limit参数
            if limit not in self.orderbook_limits["spot"]:
                self.logger.error(f"❌ 无效的limit参数: {limit}, 支持的值: {self.orderbook_limits['spot']}")
                return False
            
            self.logger.info(f"🚀 开始获取币安现货订单薄数据: {symbol} (limit: {limit})")
            
            # 构建请求URL
            url = f"{self.spot_base_url}{self.endpoints['spot_orderbook']}"
            
            # 构建请求参数
            params = {
                "symbol": symbol,
                "limit": limit
            }
            
            # 发送请求
            success, response, message = self.http_client.get(
                url=url,
                params=params
            )
            
            if not success or not response:
                self.logger.error(f"❌ 获取现货订单薄数据失败：{message}")
                return False
            
            # 验证响应数据
            if not isinstance(response, dict):
                self.logger.error(f"❌ 响应数据格式错误")
                return False
            
            # 计算统计信息
            bids = response.get('bids', [])
            asks = response.get('asks', [])
            bids_count = len(bids)
            asks_count = len(asks)
            
            # 构建元数据
            metadata = {
                "request_url": url,
                "request_params": params,
                "symbol": symbol,
                "limit": limit,
                "bids_count": bids_count,
                "asks_count": asks_count,
                "data_type": "spot_orderbook_depth",
                "market_type": "spot",
                "last_update_id": response.get('lastUpdateId')
            }
            
            # 保存数据 - 使用自定义路径保存到 binance_orderbook 文件夹下
            save_success, file_path, save_message = self._save_orderbook_data(
                symbol=symbol,
                market_type="spot",
                data=response,
                metadata=metadata
            )
            
            if save_success:
                self.logger.info(f"✅ 成功获取并保存 {symbol} 现货订单薄数据 (买单:{bids_count}, 卖单:{asks_count})")
                return True
            else:
                self.logger.error(f"❌ 保存数据失败: {save_message}")
                return False
                
        except Exception as e:
            self.logger.error(f"❌ 获取币安现货订单薄数据失败: {e}")
            return False

    def _save_orderbook_data(self, symbol: str, market_type: str, data: Dict[str, Any],
                           metadata: Dict[str, Any]) -> Tuple[bool, str, str]:
        """
        自定义保存订单薄数据到正确的目录结构

        Args:
            symbol: 交易对符号
            market_type: 市场类型 (futures/spot)
            data: 订单薄数据
            metadata: 元数据

        Returns:
            (成功标志, 文件路径, 状态信息)
        """
        try:
            from datetime import datetime
            import json

            # 构建正确的目录路径: data_storage/binance/binance_orderbook/binance_orderbook_{market_type}/binance_orderbook_{market_type}_{symbol}/
            storage_root = Path(self.file_manager.storage_root)
            target_dir = storage_root / "binance" / "binance_orderbook" / f"binance_orderbook_{market_type}" / f"binance_orderbook_{market_type}_{symbol}"
            target_dir.mkdir(parents=True, exist_ok=True)

            # 生成文件名
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"{timestamp}_binance_orderbook_{market_type}_{symbol}.json"
            file_path = target_dir / filename

            # 构建完整的数据结构
            full_data = {
                "timestamp": timestamp,
                "api_identifier": f"binance_orderbook_{market_type}_{symbol}",
                "data": data,
                "metadata": metadata or {}
            }

            # 保存数据
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(full_data, f, ensure_ascii=False, indent=2)

            self.logger.info(f"✅ API数据已保存: {file_path}")

            # 显示文件统计信息
            if self.file_manager.show_file_stats:
                self._show_file_stats(target_dir, f"binance_orderbook_{market_type}_{symbol}")

            return True, str(file_path), f"数据已保存到 {filename}"

        except Exception as e:
            error_msg = f"保存订单薄数据失败: {str(e)}"
            self.logger.error(error_msg)
            return False, "", error_msg

    def _show_file_stats(self, api_dir: Path, api_identifier: str):
        """显示文件统计信息"""
        try:
            # 获取所有JSON文件
            files = list(api_dir.glob("*.json"))

            if not files:
                self.logger.info(f"📊 {api_identifier} 文件统计: 无文件")
                return

            # 计算统计信息
            total_size = sum(f.stat().st_size for f in files)
            total_size_mb = total_size / (1024 * 1024)

            # 按修改时间排序
            files.sort(key=lambda f: f.stat().st_mtime)
            oldest_file = files[0]
            newest_file = files[-1]

            # 格式化时间
            from datetime import datetime
            oldest_time = datetime.fromtimestamp(oldest_file.stat().st_mtime).strftime('%Y-%m-%d %H:%M:%S')
            newest_time = datetime.fromtimestamp(newest_file.stat().st_mtime).strftime('%Y-%m-%d %H:%M:%S')

            self.logger.info(f"📊 {api_identifier} 文件统计:")
            self.logger.info(f"   📁 文件数量: {len(files)} 个")
            self.logger.info(f"   💾 总大小: {total_size_mb:.2f} MB")
            self.logger.info(f"   📅 最旧文件: {oldest_file.name} ({oldest_time})")
            self.logger.info(f"   🆕 最新文件: {newest_file.name} ({newest_time})")

        except Exception as e:
            self.logger.error(f"显示文件统计失败: {str(e)}")

    def start_continuous_monitoring(self):
        """
        启动持续监控模式
        根据配置文件中的设置持续获取订单薄数据
        """
        try:
            # 导入配置
            import sys
            sys.path.append(str(Path(__file__).parent.parent.parent))
            from config import BINANCE_ORDERBOOK_CONFIG

            if not BINANCE_ORDERBOOK_CONFIG.get("enable_continuous_run", False):
                self.logger.info("❌ 持续运行模式已禁用")
                return

            self.logger.info("🚀 启动币安订单薄持续监控模式")
            self.logger.info(f"📊 更新间隔: {BINANCE_ORDERBOOK_CONFIG['update_interval_seconds']}秒")
            self.logger.info(f"📈 合约交易对: {len(BINANCE_ORDERBOOK_CONFIG['symbols']['futures'])}个")
            self.logger.info(f"📉 现货交易对: {len(BINANCE_ORDERBOOK_CONFIG['symbols']['spot'])}个")

            import signal
            import threading

            # 设置停止标志
            self._stop_monitoring = False

            # 注册信号处理器
            def signal_handler(signum, frame):
                self.logger.info("🛑 收到停止信号，正在优雅关闭...")
                self._stop_monitoring = True

            signal.signal(signal.SIGINT, signal_handler)
            signal.signal(signal.SIGTERM, signal_handler)

            # 启动监控循环
            self._run_monitoring_loop(BINANCE_ORDERBOOK_CONFIG)

        except KeyboardInterrupt:
            self.logger.info("🛑 用户中断，停止监控")
        except Exception as e:
            self.logger.error(f"❌ 持续监控启动失败: {e}")

    def _run_monitoring_loop(self, config):
        """运行监控循环"""
        import time
        from datetime import datetime

        cycle_count = 0

        while not getattr(self, '_stop_monitoring', False):
            try:
                cycle_count += 1
                start_time = time.time()

                self.logger.info(f"🔄 开始第 {cycle_count} 轮数据采集 ({datetime.now().strftime('%Y-%m-%d %H:%M:%S')})")

                # 获取合约订单薄数据
                if config['symbols']['futures']:
                    self.logger.info(f"📈 开始获取 {len(config['symbols']['futures'])} 个合约订单薄...")
                    success_count = self._fetch_batch_orderbooks(
                        config['symbols']['futures'],
                        'futures',
                        config
                    )
                    self.logger.info(f"📈 合约订单薄完成: {success_count}/{len(config['symbols']['futures'])}")

                # 批次间延迟
                if config.get('delay_between_batches', 0) > 0:
                    time.sleep(config['delay_between_batches'])

                # 获取现货订单薄数据
                if config['symbols']['spot']:
                    self.logger.info(f"📉 开始获取 {len(config['symbols']['spot'])} 个现货订单薄...")
                    success_count = self._fetch_batch_orderbooks(
                        config['symbols']['spot'],
                        'spot',
                        config
                    )
                    self.logger.info(f"📉 现货订单薄完成: {success_count}/{len(config['symbols']['spot'])}")

                # 计算本轮耗时
                elapsed_time = time.time() - start_time
                self.logger.info(f"✅ 第 {cycle_count} 轮采集完成，耗时: {elapsed_time:.2f}秒")

                # 等待下一轮
                sleep_time = max(0, config['update_interval_seconds'] - elapsed_time)
                if sleep_time > 0:
                    self.logger.info(f"⏰ 等待 {sleep_time:.1f}秒 后开始下一轮...")
                    time.sleep(sleep_time)

            except Exception as e:
                self.logger.error(f"❌ 监控循环出错: {e}")
                if config.get('enable_error_recovery', True):
                    self.logger.info(f"🔄 {config.get('retry_delay', 5)}秒后重试...")
                    time.sleep(config.get('retry_delay', 5))
                else:
                    break

        self.logger.info("🏁 监控循环已停止")

    def _fetch_batch_orderbooks(self, symbols, market_type, config):
        """批量获取订单薄数据"""
        import time

        success_count = 0
        batch_size = config.get('batch_size', 3)

        # 分批处理
        for i in range(0, len(symbols), batch_size):
            batch = symbols[i:i + batch_size]

            self.logger.info(f"  📦 处理批次 {i//batch_size + 1}: {batch}")

            for symbol in batch:
                try:
                    # 获取订单薄数据
                    if market_type == 'futures':
                        success = self.fetch_futures_orderbook(symbol, config['orderbook_limit'])
                    else:
                        success = self.fetch_spot_orderbook(symbol, config['orderbook_limit'])

                    if success:
                        success_count += 1
                        self.logger.info(f"    ✅ {symbol} {market_type}")
                    else:
                        self.logger.warning(f"    ❌ {symbol} {market_type} 失败")

                    # 交易对间延迟
                    if config.get('delay_between_symbols', 0) > 0:
                        time.sleep(config['delay_between_symbols'])

                except Exception as e:
                    self.logger.error(f"    ❌ {symbol} {market_type} 错误: {e}")

            # 批次间延迟（除了最后一批）
            if i + batch_size < len(symbols) and config.get('delay_between_batches', 0) > 0:
                time.sleep(config['delay_between_batches'])

        return success_count


def main():
    """主程序入口"""
    import argparse
    import sys

    parser = argparse.ArgumentParser(description='币安订单薄数据采集器')
    parser.add_argument('--mode', choices=['single', 'continuous'], default='continuous',
                       help='运行模式: single=单次执行, continuous=持续监控 (默认: continuous)')
    parser.add_argument('--symbol', type=str, help='单次模式下的交易对符号 (如: BTCUSDT)')
    parser.add_argument('--market', choices=['futures', 'spot'], default='futures',
                       help='市场类型: futures=合约, spot=现货 (默认: futures)')
    parser.add_argument('--limit', type=int, default=50, help='订单薄深度 (默认: 50)')

    args = parser.parse_args()

    try:
        # 初始化请求器
        requester = BinanceOrderbookRequester()

        if args.mode == 'single':
            # 单次执行模式
            if not args.symbol:
                print("❌ 单次模式需要指定 --symbol 参数")
                sys.exit(1)

            print(f"🚀 单次获取 {args.symbol} {args.market} 订单薄数据...")

            if args.market == 'futures':
                success = requester.fetch_futures_orderbook(args.symbol, args.limit)
            else:
                success = requester.fetch_spot_orderbook(args.symbol, args.limit)

            if success:
                print(f"✅ 成功获取 {args.symbol} {args.market} 订单薄数据")
            else:
                print(f"❌ 获取 {args.symbol} {args.market} 订单薄数据失败")
                sys.exit(1)

        else:
            # 持续监控模式
            print("🚀 启动持续监控模式...")
            print("💡 按 Ctrl+C 停止监控")
            requester.start_continuous_monitoring()

    except KeyboardInterrupt:
        print("\n🛑 用户中断，程序退出")
    except Exception as e:
        print(f"❌ 程序执行失败: {e}")
        sys.exit(1)


    def fetch_multiple_orderbooks(self, symbols: List[str], market_type: str = "futures",
                                limit: int = 100) -> Dict[str, bool]:
        """
        批量获取多个交易对的订单薄数据
        
        Args:
            symbols: 交易对列表
            market_type: 市场类型 ("futures" 或 "spot")
            limit: 深度限制
            
        Returns:
            Dict[str, bool]: 每个交易对的获取结果
        """
        results = {}
        
        self.logger.info(f"🔄 开始批量获取 {len(symbols)} 个交易对的 {market_type} 订单薄数据...")
        
        for i, symbol in enumerate(symbols, 1):
            try:
                self.logger.info(f"📊 处理 {i}/{len(symbols)}: {symbol}")
                
                if market_type == "futures":
                    success = self.fetch_futures_orderbook(symbol, limit)
                elif market_type == "spot":
                    success = self.fetch_spot_orderbook(symbol, limit)
                else:
                    self.logger.error(f"❌ 不支持的市场类型: {market_type}")
                    success = False
                
                results[symbol] = success
                
                # 添加延迟避免触发限制
                if i < len(symbols):  # 最后一个不需要延迟
                    time.sleep(BINANCE_CONFIG["rate_limit"]["delay_between_requests"])
                
            except Exception as e:
                self.logger.error(f"❌ 获取 {symbol} 订单薄数据失败: {e}")
                results[symbol] = False
        
        success_count = sum(1 for success in results.values() if success)
        self.logger.info(f"📊 批量获取完成: {success_count}/{len(symbols)} 成功")
        
        return results
    
    def get_latest_orderbook_data(self, symbol: str, market_type: str = "futures"):
        """获取最新缓存的订单薄数据"""
        try:
            # 直接从正确的目录加载数据
            storage_root = Path(self.file_manager.storage_root)
            target_dir = storage_root / "binance" / "binance_orderbook" / f"binance_orderbook_{market_type}_{symbol}"

            if not target_dir.exists():
                self.logger.error(f"❌ 目录不存在: {target_dir}")
                return None

            # 查找最新的数据文件
            files = list(target_dir.glob("*.json"))
            if not files:
                self.logger.error(f"❌ 未找到数据文件: {target_dir}")
                return None

            # 按修改时间排序，获取最新文件
            latest_file = max(files, key=lambda f: f.stat().st_mtime)

            # 读取数据
            import json
            with open(latest_file, 'r', encoding='utf-8') as f:
                data = json.load(f)

            self.logger.info(f"✅ 已加载 {symbol} {market_type} 订单薄数据: {latest_file.name}")
            return data

        except Exception as e:
            self.logger.error(f"❌ 加载 {symbol} {market_type} 订单薄数据失败: {e}")
            return None
    
    def is_orderbook_data_fresh(self, symbol: str, market_type: str = "futures",
                              max_age_seconds: int = 60) -> bool:
        """检查订单薄数据是否新鲜（默认1分钟）"""
        try:
            # 直接检查正确的目录
            storage_root = Path(self.file_manager.storage_root)
            target_dir = storage_root / "binance" / "binance_orderbook" / f"binance_orderbook_{market_type}_{symbol}"

            if not target_dir.exists():
                return False

            # 查找最新的数据文件
            files = list(target_dir.glob("*.json"))
            if not files:
                return False

            # 获取最新文件的修改时间
            latest_file = max(files, key=lambda f: f.stat().st_mtime)
            file_age = time.time() - latest_file.stat().st_mtime

            return file_age <= max_age_seconds

        except Exception as e:
            self.logger.error(f"检查数据新鲜度失败: {str(e)}")
            return False

    def get_all_symbols_data(self, market_type: str = "futures", max_age_seconds: int = 300) -> Dict[str, Any]:
        """
        获取所有币种的订单薄数据

        Args:
            market_type: 市场类型 ("futures" 或 "spot")
            max_age_seconds: 数据最大年龄（秒），默认5分钟

        Returns:
            Dict[str, Any]: 所有币种的数据，格式为 {symbol: data}
        """
        from pathlib import Path

        all_data = {}

        try:
            # 构建目录路径 - 按照您的存储结构
            base_path = Path(self.file_manager.storage_root) / "binance" / "binance_orderbook"

            if not base_path.exists():
                self.logger.warning(f"⚠️ 目录不存在: {base_path}")
                return all_data

            # 查找所有订单薄相关的目录
            pattern = f"binance_orderbook_{market_type}_"

            # 遍历所有目录，找到匹配的订单薄数据
            for api_dir in base_path.iterdir():
                if api_dir.is_dir() and api_dir.name.startswith(pattern):
                    # 从目录名提取币种符号
                    symbol = api_dir.name.replace(pattern, "")

                    # 检查数据是否新鲜
                    if self.is_orderbook_data_fresh(symbol, market_type, max_age_seconds):
                        data = self.get_latest_orderbook_data(symbol, market_type)
                        if data:
                            all_data[symbol] = data
                            self.logger.debug(f"✅ 加载 {symbol} {market_type} 数据")
                        else:
                            self.logger.warning(f"⚠️ {symbol} {market_type} 数据加载失败")
                    else:
                        self.logger.debug(f"⏰ {symbol} {market_type} 数据过期，跳过")

            self.logger.info(f"📊 成功加载 {len(all_data)} 个币种的 {market_type} 订单薄数据")
            return all_data

        except Exception as e:
            self.logger.error(f"❌ 获取所有币种数据失败: {e}")
            return all_data

    def get_symbols_list(self, market_type: str = "futures") -> List[str]:
        """
        获取所有已存储数据的币种列表

        Args:
            market_type: 市场类型 ("futures" 或 "spot")

        Returns:
            List[str]: 币种列表
        """
        from pathlib import Path

        symbols = []

        try:
            # 构建目录路径 - 按照您的存储结构
            base_path = Path(self.file_manager.storage_root) / "binance" / "binance_orderbook"

            if base_path.exists():
                # 查找所有订单薄相关的目录
                pattern = f"binance_orderbook_{market_type}_"

                # 遍历所有目录，找到匹配的订单薄数据
                for api_dir in base_path.iterdir():
                    if api_dir.is_dir() and api_dir.name.startswith(pattern):
                        # 从目录名提取币种符号
                        symbol = api_dir.name.replace(pattern, "")
                        symbols.append(symbol)

                symbols.sort()  # 按字母顺序排序
                self.logger.info(f"📋 找到 {len(symbols)} 个 {market_type} 币种: {symbols[:5]}{'...' if len(symbols) > 5 else ''}")
            else:
                self.logger.warning(f"⚠️ 目录不存在: {base_path}")

            return symbols

        except Exception as e:
            self.logger.error(f"❌ 获取币种列表失败: {e}")
            return symbols

if __name__ == "__main__":
    main()
