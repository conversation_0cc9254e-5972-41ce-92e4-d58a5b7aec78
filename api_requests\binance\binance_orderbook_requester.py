#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
币安订单簿数据获取器 - 清理版本
支持全量活跃币种获取和手动配置模式
"""

import sys
import time
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple

# 添加项目路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

try:
    from config import BINANCE_CONFIG, BINANCE_ORDERBOOK_CONFIG
    from main import FileManager, HTTPClient, setup_logger
except ImportError as e:
    print(f"导入错误: {e}")
    print("请确保在项目根目录运行此脚本")
    sys.exit(1)


class BinanceOrderbookRequester:
    """币安订单簿数据获取器"""
    
    def __init__(self):
        """初始化"""
        # 设置日志
        self.logger = setup_logger("api_requests.binance_orderbook")
        
        # 初始化文件管理器
        self.file_manager = FileManager()
        
        # 初始化HTTP客户端
        self.http_client = HTTPClient()
        
        # 缓存
        self._futures_symbols_cache = None
        self._spot_symbols_cache = None
        self._cache_timestamp = 0
        self._cache_duration = 300  # 5分钟缓存
        
        # 被阻止的交易对
        self.blocked_symbols = {'BNXUSDT', 'ALPACAUSDT'}
        
        self.logger.info("✅ binance_orderbook 请求器初始化完成")
    
    def get_active_futures_symbols(self, force_refresh: bool = False, max_symbols: int = 500) -> List[str]:
        """
        获取活跃的USDT合约交易对
        
        Args:
            force_refresh: 是否强制刷新缓存
            max_symbols: 最大返回数量
            
        Returns:
            List[str]: 按交易量排序的活跃交易对列表
        """
        try:
            current_time = time.time()
            
            # 检查缓存
            if (not force_refresh and 
                self._futures_symbols_cache and 
                current_time - self._cache_timestamp < self._cache_duration):
                return self._futures_symbols_cache[:max_symbols]
            
            self.logger.info("🔄 开始获取活跃合约交易对...")
            
            # 获取交易所信息
            exchange_info_url = f"{BINANCE_CONFIG['futures_base_url']}/fapi/v1/exchangeInfo"
            success, exchange_response, message = self.http_client.get(exchange_info_url)

            if not success or not exchange_response:
                self.logger.error(f"❌ 获取合约交易所信息失败: {message}")
                return []
            
            # 筛选活跃的USDT永续合约
            active_symbols = []
            for symbol_info in exchange_response.get('symbols', []):
                if (symbol_info.get('status') == 'TRADING' and
                    symbol_info.get('quoteAsset') == 'USDT' and
                    symbol_info.get('contractType') == 'PERPETUAL' and
                    symbol_info.get('symbol') not in self.blocked_symbols):
                    active_symbols.append(symbol_info['symbol'])
            
            self.logger.info(f"📊 找到 {len(active_symbols)} 个活跃合约交易对")
            
            # 获取24小时交易数据进行排序
            ticker_url = f"{BINANCE_CONFIG['futures_base_url']}/fapi/v1/ticker/24hr"
            ticker_success, ticker_response, ticker_message = self.http_client.get(ticker_url)

            if ticker_success and ticker_response:
                # 创建交易量映射
                volume_map = {}
                for ticker in ticker_response:
                    symbol = ticker.get('symbol')
                    if symbol in active_symbols:
                        volume_map[symbol] = float(ticker.get('quoteVolume', 0))
                
                # 按交易量排序
                active_symbols.sort(key=lambda x: volume_map.get(x, 0), reverse=True)
                
                top_10 = active_symbols[:10]
                self.logger.info(f"✅ 按交易量排序完成，前10名: {top_10}")
            
            # 更新缓存
            self._futures_symbols_cache = active_symbols
            self._cache_timestamp = current_time
            
            self.logger.info(f"✅ 获取到 {len(active_symbols)} 个活跃合约交易对（已按交易量排序）")
            return active_symbols[:max_symbols]
            
        except Exception as e:
            self.logger.error(f"❌ 获取活跃合约交易对失败: {e}")
            return []
    
    def get_active_spot_symbols(self, force_refresh: bool = False, max_symbols: int = 500) -> List[str]:
        """
        获取活跃的USDT现货交易对
        
        Args:
            force_refresh: 是否强制刷新缓存
            max_symbols: 最大返回数量
            
        Returns:
            List[str]: 按交易量排序的活跃交易对列表
        """
        try:
            current_time = time.time()
            
            # 检查缓存
            if (not force_refresh and 
                self._spot_symbols_cache and 
                current_time - self._cache_timestamp < self._cache_duration):
                return self._spot_symbols_cache[:max_symbols]
            
            self.logger.info("🔄 开始获取活跃现货交易对...")
            
            # 获取交易所信息
            exchange_info_url = f"{BINANCE_CONFIG['spot_base_url']}/api/v3/exchangeInfo"
            success, exchange_response, message = self.http_client.get(exchange_info_url)

            if not success or not exchange_response:
                self.logger.error(f"❌ 获取现货交易所信息失败: {message}")
                return []
            
            # 筛选活跃的USDT交易对
            active_symbols = []
            for symbol_info in exchange_response.get('symbols', []):
                if (symbol_info.get('status') == 'TRADING' and
                    symbol_info.get('quoteAsset') == 'USDT' and
                    symbol_info.get('symbol') not in self.blocked_symbols):
                    active_symbols.append(symbol_info['symbol'])
            
            self.logger.info(f"📊 找到 {len(active_symbols)} 个活跃现货交易对")
            
            # 获取24小时交易数据进行排序
            ticker_url = f"{BINANCE_CONFIG['spot_base_url']}/api/v3/ticker/24hr"
            ticker_success, ticker_response, ticker_message = self.http_client.get(ticker_url)

            if ticker_success and ticker_response:
                # 创建交易量映射
                volume_map = {}
                for ticker in ticker_response:
                    symbol = ticker.get('symbol')
                    if symbol in active_symbols:
                        volume_map[symbol] = float(ticker.get('quoteVolume', 0))
                
                # 按交易量排序
                active_symbols.sort(key=lambda x: volume_map.get(x, 0), reverse=True)
                
                top_10 = active_symbols[:10]
                self.logger.info(f"✅ 按交易量排序完成，前10名: {top_10}")
            
            # 更新缓存
            self._spot_symbols_cache = active_symbols
            self._cache_timestamp = current_time
            
            self.logger.info(f"✅ 获取到 {len(active_symbols)} 个活跃现货交易对（已按交易量排序）")
            return active_symbols[:max_symbols]
            
        except Exception as e:
            self.logger.error(f"❌ 获取活跃现货交易对失败: {e}")
            return []

    def fetch_futures_orderbook(self, symbol: str, limit: int = 1000) -> bool:
        """
        获取合约订单薄深度数据

        Args:
            symbol: 交易对符号，如 BTCUSDT
            limit: 深度限制 (5, 10, 20, 50, 100, 500, 1000)

        Returns:
            bool: 是否成功获取数据
        """
        try:
            # 验证limit参数
            valid_limits = [5, 10, 20, 50, 100, 500, 1000]
            if limit not in valid_limits:
                self.logger.error(f"❌ 无效的limit参数: {limit}, 支持的值: {valid_limits}")
                return False

            self.logger.info(f"🚀 开始获取币安合约订单薄数据: {symbol} (limit: {limit})")

            # 构建请求URL
            url = f"{BINANCE_CONFIG['futures_base_url']}/fapi/v1/depth"

            # 构建请求参数
            params = {
                "symbol": symbol,
                "limit": limit
            }

            # 发送请求
            success, response, message = self.http_client.get(url, params=params)

            if not success or not response:
                self.logger.error(f"❌ 获取合约订单薄数据失败: {message}")
                return False

            # 验证响应数据
            if not isinstance(response, dict):
                self.logger.error(f"❌ 响应数据格式错误")
                return False

            # 计算统计信息
            bids = response.get('bids', [])
            asks = response.get('asks', [])
            bids_count = len(bids)
            asks_count = len(asks)

            # 构建元数据
            metadata = {
                "request_url": url,
                "request_params": params,
                "symbol": symbol,
                "limit": limit,
                "bids_count": bids_count,
                "asks_count": asks_count,
                "data_type": "futures_orderbook_depth",
                "market_type": "futures",
                "last_update_id": response.get('lastUpdateId'),
                "timestamp": int(time.time() * 1000)
            }

            # 保存数据
            success, file_path, filename = self._save_orderbook_data(symbol, "futures", response, metadata)

            if success:
                self.logger.info(f"✅ 合约订单薄数据获取成功: {symbol} -> {filename}")
                return True
            else:
                self.logger.error(f"❌ 合约订单薄数据保存失败: {symbol}")
                return False

        except Exception as e:
            self.logger.error(f"❌ 获取合约订单薄数据异常: {symbol} - {e}")
            return False

    def _save_orderbook_data(self, symbol: str, market_type: str, data: Dict[str, Any],
                           metadata: Dict[str, Any]) -> Tuple[bool, str, str]:
        """
        保存订单簿数据到指定的目录结构

        Args:
            symbol: 交易对符号
            market_type: 市场类型 ("futures" 或 "spot")
            data: 订单簿数据
            metadata: 元数据

        Returns:
            Tuple[bool, str, str]: (是否成功, 文件路径, 文件名)
        """
        try:
            # 构建正确的目录路径: data_storage/binance/binance_orderbook/binance_orderbook_{market_type}/binance_orderbook_{market_type}_{symbol}/
            storage_root = Path(self.file_manager.storage_root)
            target_dir = storage_root / "binance" / "binance_orderbook" / f"binance_orderbook_{market_type}" / f"binance_orderbook_{market_type}_{symbol}"

            # 确保目录存在
            target_dir.mkdir(parents=True, exist_ok=True)

            # 生成文件名
            from datetime import datetime
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"{timestamp}_binance_orderbook_{market_type}_{symbol}.json"
            file_path = target_dir / filename

            # 构建完整的数据结构
            full_data = {
                "metadata": metadata,
                "data": data
            }

            # 保存文件
            import json
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(full_data, f, ensure_ascii=False, indent=2)

            return True, str(file_path), filename

        except Exception as e:
            self.logger.error(f"❌ 保存订单簿数据失败: {e}")
            return False, "", ""

    def fetch_spot_orderbook(self, symbol: str, limit: int = 1000) -> bool:
        """
        获取现货订单薄深度数据

        Args:
            symbol: 交易对符号，如 BTCUSDT
            limit: 深度限制 (5, 10, 20, 50, 100, 500, 1000, 5000)

        Returns:
            bool: 是否成功获取数据
        """
        try:
            # 验证limit参数
            valid_limits = [5, 10, 20, 50, 100, 500, 1000, 5000]
            if limit not in valid_limits:
                self.logger.error(f"❌ 无效的limit参数: {limit}, 支持的值: {valid_limits}")
                return False

            self.logger.info(f"🚀 开始获取币安现货订单薄数据: {symbol} (limit: {limit})")

            # 构建请求URL
            url = f"{BINANCE_CONFIG['spot_base_url']}/api/v3/depth"

            # 构建请求参数
            params = {
                "symbol": symbol,
                "limit": limit
            }

            # 发送请求
            success, response, message = self.http_client.get(url, params=params)

            if not success or not response:
                self.logger.error(f"❌ 获取现货订单薄数据失败: {message}")
                return False

            # 验证响应数据
            if not isinstance(response, dict):
                self.logger.error(f"❌ 响应数据格式错误")
                return False

            # 计算统计信息
            bids = response.get('bids', [])
            asks = response.get('asks', [])
            bids_count = len(bids)
            asks_count = len(asks)

            # 构建元数据
            metadata = {
                "request_url": url,
                "request_params": params,
                "symbol": symbol,
                "limit": limit,
                "bids_count": bids_count,
                "asks_count": asks_count,
                "data_type": "spot_orderbook_depth",
                "market_type": "spot",
                "last_update_id": response.get('lastUpdateId'),
                "timestamp": int(time.time() * 1000)
            }

            # 保存数据
            success, file_path, filename = self._save_orderbook_data(symbol, "spot", response, metadata)

            if success:
                self.logger.info(f"✅ 现货订单薄数据获取成功: {symbol} -> {filename}")
                return True
            else:
                self.logger.error(f"❌ 现货订单薄数据保存失败: {symbol}")
                return False

        except Exception as e:
            self.logger.error(f"❌ 获取现货订单薄数据异常: {symbol} - {e}")
            return False

    def fetch_all_active_futures_orderbooks(self, max_symbols: int = 150,
                                          batch_size: int = 30, limit: int = 100) -> Dict[str, Any]:
        """
        批量获取所有活跃合约交易对的订单簿数据

        Args:
            max_symbols: 最大处理的交易对数量
            batch_size: 批量处理大小
            limit: 订单簿深度限制

        Returns:
            Dict[str, Any]: 处理结果统计
        """
        try:
            # 获取活跃交易对
            active_symbols = self.get_active_futures_symbols(max_symbols=max_symbols)
            if not active_symbols:
                self.logger.error("❌ 无法获取活跃合约交易对列表")
                return {"success": False, "message": "无法获取活跃交易对", "results": {}}

            # 限制处理的交易对数量
            target_symbols = active_symbols[:max_symbols]
            self.logger.info(f"🚀 开始批量获取 {len(target_symbols)} 个合约交易对的订单簿数据")

            results = {}
            success_count = 0
            total_count = len(target_symbols)

            # 分批处理
            for i in range(0, len(target_symbols), batch_size):
                batch_symbols = target_symbols[i:i+batch_size]
                batch_success = 0
                batch_num = i // batch_size + 1

                self.logger.info(f"📦 处理第 {batch_num} 批: {len(batch_symbols)} 个交易对")

                for symbol in batch_symbols:
                    try:
                        success = self.fetch_futures_orderbook(symbol, limit)
                        results[symbol] = success
                        if success:
                            success_count += 1
                            batch_success += 1

                        # 单个交易对间的延迟
                        time.sleep(0.5)

                    except Exception as e:
                        self.logger.error(f"❌ 获取 {symbol} 合约订单簿失败: {e}")
                        results[symbol] = False

                # 批次间延迟
                if i + batch_size < len(target_symbols):
                    self.logger.info(f"⏱️ 批次 {batch_num} 完成 ({batch_success}/{len(batch_symbols)} 成功)，等待 2 秒...")
                    time.sleep(2)
                else:
                    self.logger.info(f"✅ 批次 {batch_num} 完成 ({batch_success}/{len(batch_symbols)} 成功)")

            success_rate = (success_count / total_count) * 100 if total_count > 0 else 0

            self.logger.info(f"🎯 批量获取完成: {success_count}/{total_count} 成功 ({success_rate:.1f}%)")

            return {
                "success": True,
                "total_symbols": total_count,
                "success_count": success_count,
                "success_rate": success_rate,
                "results": results,
                "message": f"成功获取 {success_count}/{total_count} 个交易对的订单簿数据"
            }

        except Exception as e:
            self.logger.error(f"❌ 批量获取合约订单簿数据失败: {e}")
            return {"success": False, "message": str(e), "results": {}}

    def fetch_all_active_spot_orderbooks(self, max_symbols: int = 100,
                                       batch_size: int = 20, limit: int = 100) -> Dict[str, Any]:
        """
        批量获取所有活跃现货交易对的订单簿数据

        Args:
            max_symbols: 最大处理的交易对数量
            batch_size: 批量处理大小
            limit: 订单簿深度限制

        Returns:
            Dict[str, Any]: 处理结果统计
        """
        try:
            # 获取活跃交易对
            active_symbols = self.get_active_spot_symbols(max_symbols=max_symbols)
            if not active_symbols:
                self.logger.error("❌ 无法获取活跃现货交易对列表")
                return {"success": False, "message": "无法获取活跃交易对", "results": {}}

            # 限制处理的交易对数量
            target_symbols = active_symbols[:max_symbols]
            self.logger.info(f"🚀 开始批量获取 {len(target_symbols)} 个现货交易对的订单簿数据")

            results = {}
            success_count = 0
            total_count = len(target_symbols)

            # 分批处理
            for i in range(0, len(target_symbols), batch_size):
                batch_symbols = target_symbols[i:i+batch_size]
                batch_success = 0
                batch_num = i // batch_size + 1

                self.logger.info(f"📦 处理第 {batch_num} 批: {len(batch_symbols)} 个交易对")

                for symbol in batch_symbols:
                    try:
                        success = self.fetch_spot_orderbook(symbol, limit)
                        results[symbol] = success
                        if success:
                            success_count += 1
                            batch_success += 1

                        # 单个交易对间的延迟
                        time.sleep(0.5)

                    except Exception as e:
                        self.logger.error(f"❌ 获取 {symbol} 现货订单簿失败: {e}")
                        results[symbol] = False

                # 批次间延迟
                if i + batch_size < len(target_symbols):
                    self.logger.info(f"⏱️ 批次 {batch_num} 完成 ({batch_success}/{len(batch_symbols)} 成功)，等待 2 秒...")
                    time.sleep(2)
                else:
                    self.logger.info(f"✅ 批次 {batch_num} 完成 ({batch_success}/{len(batch_symbols)} 成功)")

            success_rate = (success_count / total_count) * 100 if total_count > 0 else 0

            self.logger.info(f"🎯 批量获取完成: {success_count}/{total_count} 成功 ({success_rate:.1f}%)")

            return {
                "success": True,
                "total_symbols": total_count,
                "success_count": success_count,
                "success_rate": success_rate,
                "results": results,
                "message": f"成功获取 {success_count}/{total_count} 个交易对的订单簿数据"
            }

        except Exception as e:
            self.logger.error(f"❌ 批量获取现货订单簿数据失败: {e}")
            return {"success": False, "message": str(e), "results": {}}

    def start_continuous_monitoring(self):
        """
        启动持续监控模式
        根据配置文件设置进行持续的数据获取
        """
        try:
            self.logger.info("🚀 启动币安订单簿持续监控模式")

            # 获取配置
            fetch_mode = BINANCE_ORDERBOOK_CONFIG.get("fetch_mode", "manual")
            update_interval = BINANCE_ORDERBOOK_CONFIG.get("update_interval_seconds", 300)

            self.logger.info(f"📊 监控模式: {fetch_mode}")
            self.logger.info(f"⏱️ 更新间隔: {update_interval} 秒")

            if fetch_mode == "all_active":
                self._run_all_active_monitoring_loop()
            else:
                self._run_manual_monitoring_loop()

        except KeyboardInterrupt:
            self.logger.info("🛑 用户中断，停止监控")
        except Exception as e:
            self.logger.error(f"❌ 持续监控失败: {e}")
            raise

    def _run_all_active_monitoring_loop(self):
        """运行全量活跃币种监控循环"""
        update_interval = BINANCE_ORDERBOOK_CONFIG.get("update_interval_seconds", 300)
        max_futures = BINANCE_ORDERBOOK_CONFIG.get("max_futures_symbols", 150)
        max_spot = BINANCE_ORDERBOOK_CONFIG.get("max_spot_symbols", 100)
        futures_batch_size = BINANCE_ORDERBOOK_CONFIG.get("futures_batch_size", 30)
        spot_batch_size = BINANCE_ORDERBOOK_CONFIG.get("spot_batch_size", 20)
        orderbook_limit = BINANCE_ORDERBOOK_CONFIG.get("orderbook_limit", 100)

        self.logger.info(f"🎯 全量监控配置: 合约{max_futures}个, 现货{max_spot}个")

        cycle_count = 0

        while True:
            cycle_count += 1
            cycle_start_time = time.time()

            self.logger.info(f"🔄 开始第 {cycle_count} 轮数据获取...")

            try:
                # 获取合约数据
                self.logger.info("📈 开始获取合约订单簿数据...")
                futures_result = self.fetch_all_active_futures_orderbooks(
                    max_symbols=max_futures,
                    batch_size=futures_batch_size,
                    limit=orderbook_limit
                )

                # 获取现货数据
                self.logger.info("📉 开始获取现货订单簿数据...")
                spot_result = self.fetch_all_active_spot_orderbooks(
                    max_symbols=max_spot,
                    batch_size=spot_batch_size,
                    limit=orderbook_limit
                )

                # 统计结果
                futures_success = futures_result.get("success_count", 0)
                futures_total = futures_result.get("total_symbols", 0)
                spot_success = spot_result.get("success_count", 0)
                spot_total = spot_result.get("total_symbols", 0)

                cycle_duration = time.time() - cycle_start_time

                self.logger.info(f"✅ 第 {cycle_count} 轮完成:")
                self.logger.info(f"   📈 合约: {futures_success}/{futures_total} 成功")
                self.logger.info(f"   📉 现货: {spot_success}/{spot_total} 成功")
                self.logger.info(f"   ⏱️ 耗时: {cycle_duration:.1f} 秒")

                # 等待下一轮
                self.logger.info(f"⏳ 等待 {update_interval} 秒后开始下一轮...")
                time.sleep(update_interval)

            except Exception as e:
                self.logger.error(f"❌ 第 {cycle_count} 轮获取失败: {e}")
                self.logger.info(f"⏳ 等待 {update_interval} 秒后重试...")
                time.sleep(update_interval)

    def _run_manual_monitoring_loop(self):
        """运行手动配置监控循环"""
        update_interval = BINANCE_ORDERBOOK_CONFIG.get("update_interval_seconds", 300)
        symbols_config = BINANCE_ORDERBOOK_CONFIG.get("symbols", {})
        futures_symbols = symbols_config.get("futures", [])
        spot_symbols = symbols_config.get("spot", [])
        orderbook_limit = BINANCE_ORDERBOOK_CONFIG.get("orderbook_limit", 100)

        self.logger.info(f"🎯 手动监控配置: 合约{len(futures_symbols)}个, 现货{len(spot_symbols)}个")

        if not futures_symbols and not spot_symbols:
            self.logger.error("❌ 未配置任何交易对，请检查config.py中的symbols配置")
            return

        cycle_count = 0

        while True:
            cycle_count += 1
            cycle_start_time = time.time()

            self.logger.info(f"🔄 开始第 {cycle_count} 轮数据获取...")

            try:
                futures_success = 0
                spot_success = 0

                # 获取合约数据
                if futures_symbols:
                    self.logger.info(f"📈 获取 {len(futures_symbols)} 个合约订单簿...")
                    for symbol in futures_symbols:
                        if self.fetch_futures_orderbook(symbol, orderbook_limit):
                            futures_success += 1
                        time.sleep(0.5)  # 避免频率限制

                # 获取现货数据
                if spot_symbols:
                    self.logger.info(f"📉 获取 {len(spot_symbols)} 个现货订单簿...")
                    for symbol in spot_symbols:
                        if self.fetch_spot_orderbook(symbol, orderbook_limit):
                            spot_success += 1
                        time.sleep(0.5)  # 避免频率限制

                cycle_duration = time.time() - cycle_start_time

                self.logger.info(f"✅ 第 {cycle_count} 轮完成:")
                self.logger.info(f"   📈 合约: {futures_success}/{len(futures_symbols)} 成功")
                self.logger.info(f"   📉 现货: {spot_success}/{len(spot_symbols)} 成功")
                self.logger.info(f"   ⏱️ 耗时: {cycle_duration:.1f} 秒")

                # 等待下一轮
                self.logger.info(f"⏳ 等待 {update_interval} 秒后开始下一轮...")
                time.sleep(update_interval)

            except Exception as e:
                self.logger.error(f"❌ 第 {cycle_count} 轮获取失败: {e}")
                self.logger.info(f"⏳ 等待 {update_interval} 秒后重试...")
                time.sleep(update_interval)


if __name__ == "__main__":
    # 简单测试
    requester = BinanceOrderbookRequester()
    
    print("测试获取活跃合约交易对...")
    futures_symbols = requester.get_active_futures_symbols(max_symbols=10)
    print(f"前10个合约: {futures_symbols}")
    
    print("\n测试获取活跃现货交易对...")
    spot_symbols = requester.get_active_spot_symbols(max_symbols=10)
    print(f"前10个现货: {spot_symbols}")
