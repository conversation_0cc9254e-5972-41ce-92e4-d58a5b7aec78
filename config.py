#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
项目配置文件
存储所有API的URL、密钥和数据存储路径
"""

import os
from pathlib import Path

# ========== 基础配置 ==========

# 项目根目录
PROJECT_ROOT = Path(__file__).parent

# 数据存储根目录
DATA_STORAGE_ROOT = PROJECT_ROOT / "data_storage"

# 日志配置
LOG_LEVEL = os.getenv('LOG_LEVEL', 'INFO')
LOG_FORMAT = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'

# ========== API配置 ==========

# Binance API配置
BINANCE_CONFIG = {
    "futures_base_url": "https://fapi.binance.com",
    "spot_base_url": "https://api.binance.com",
    "endpoints": {
        # 合约端点
        "futures_exchange_info": "/fapi/v1/exchangeInfo",
        "futures_ticker_24hr": "/fapi/v1/ticker/24hr", 
        "futures_premium_index": "/fapi/v1/premiumIndex",
        "futures_funding_rate": "/fapi/v1/fundingRate",
        "futures_open_interest": "/fapi/v1/openInterest",
        "futures_depth": "/fapi/v1/depth",
        "futures_klines": "/fapi/v1/klines",
        "futures_continuous_klines": "/fapi/v1/continuousKlines",
        "futures_index_price_klines": "/fapi/v1/indexPriceKlines",
        "futures_mark_price_klines": "/fapi/v1/markPriceKlines",
        "futures_premium_index_klines": "/fapi/v1/premiumIndexKlines",
        "futures_trades": "/fapi/v1/trades",
        "futures_agg_trades": "/fapi/v1/aggTrades",
        "futures_top_long_short_position_ratio": "/futures/data/topLongShortPositionRatio",
        "futures_top_long_short_account_ratio": "/futures/data/topLongShortAccountRatio",
        "futures_global_long_short_account_ratio": "/futures/data/globalLongShortAccountRatio",
        "futures_taker_long_short_ratio": "/futures/data/takerlongshortRatio",
        "futures_basis": "/futures/data/basis",
        "futures_index_info": "/fapi/v1/indexInfo",
        "futures_asset_index": "/fapi/v1/assetIndex",
        "futures_constituents": "/fapi/v1/constituents",
        "futures_funding_info": "/fapi/v1/fundingInfo",
        "futures_book_ticker": "/fapi/v1/ticker/bookTicker",
        "futures_ticker_price": "/fapi/v3/ticker/price",
        "futures_open_interest_hist": "/futures/data/openInterestHist",
        
        # 现货端点
        "spot_exchange_info": "/api/v3/exchangeInfo",
        "spot_ticker_24hr": "/api/v3/ticker/24hr",
        "spot_depth": "/api/v3/depth",

        # 订单薄端点
        "futures_orderbook": "/fapi/v1/depth",
        "spot_orderbook": "/api/v3/depth",

        "spot_klines": "/api/v3/klines",
        "spot_trades": "/api/v3/trades",
        "spot_agg_trades": "/api/v3/aggTrades",
        "spot_ticker_price": "/api/v3/ticker/price",
        "spot_book_ticker": "/api/v3/ticker/bookTicker"
    },
    "rate_limit": {
        "requests_per_minute": 1200,
        "weight_per_minute": 2400,
        "delay_between_requests": 0.05
    },
    "orderbook_limits": {
        "futures": [5, 10, 20, 50, 100, 500, 1000],
        "spot": [5, 10, 20, 50, 100, 500, 1000, 5000]
    },
    "timeout": 10,
    "max_retries": 3
}

# 币安订单薄专门配置
BINANCE_ORDERBOOK_CONFIG = {
    "enable_continuous_run": True,     # 启用持续运行
    "update_interval_seconds": 300,     # 更新间隔（秒）

    # 获取模式配置
    "fetch_mode": "all_active",        # "all_active" 获取所有活跃币种, "manual" 使用手动配置的币种
    "max_futures_symbols": 150,        # 最大合约交易对数量
    "max_spot_symbols": 100,           # 最大现货交易对数量

    # 手动配置的交易对（当 fetch_mode = "manual" 时使用）
    "symbols": {
        "futures": [                   # 合约交易对
            "BTCUSDT", "ETHUSDT", "BNBUSDT", "ADAUSDT",
            "SOLUSDT", "DOGEUSDT", "XRPUSDT", "LINKUSDT",
            "AVAXUSDT", "DOTUSDT", "MATICUSDT", "LTCUSDT"
        ],
        "spot": [                      # 现货交易对
            "BTCUSDT", "ETHUSDT", "BNBUSDT", "ADAUSDT",
            "SOLUSDT", "DOGEUSDT", "XRPUSDT", "LINKUSDT"
        ]
    },

    # 订单薄配置
    "orderbook_limit": 50,             # 订单薄深度
    "futures_batch_size": 30,          # 合约批量处理大小
    "spot_batch_size": 20,             # 现货批量处理大小
    "delay_between_batches": 2,        # 批次间延迟（秒）
    "delay_between_symbols": 0.5,      # 单个交易对间延迟（秒）

    # 请求控制
    "max_concurrent_requests": 1,      # 最大并发请求数
    "retry_on_error": True,            # 错误时重试
    "max_retries": 3,                  # 最大重试次数
    "retry_delay": 5,                  # 重试延迟（秒）

    # 日志和存储
    "log_progress": True,              # 记录进度日志
    "save_individual_files": True,     # 为每个交易对保存独立文件
    "enable_error_recovery": True,     # 启用错误恢复
    "show_statistics": True,           # 显示统计信息

    # 缓存配置
    "symbols_cache_duration": 300,     # 交易对缓存时间（秒）
    "force_refresh_symbols": False     # 是否强制刷新交易对列表
}

# CoinGlass API配置
COINGLASS_CONFIG = {
    "base_url": "https://open-api-v4.coinglass.com",
    "legacy_base_url": "https://fapi.coinglass.com",
    "api_key": os.getenv('COINGLASS_API_KEY', 'c40fbbee201d4dfab3a4b62f37f0b610'),
    "endpoints": {
        # V4 API端点 - 期货
        "v4_futures_coins_markets": "/api/futures/coins-markets",
        "v4_futures_price_history": "/api/futures/price/history", 
        "v4_futures_rsi_list": "/api/futures/rsi/list",
        "v4_futures_liquidation_coin_list": "/api/futures/liquidation/coin-list",
        "v4_futures_orderbook_large_limit_order": "/api/futures/orderbook/large-limit-order",
        "v4_futures_orderbook_large_limit_order_history": "/api/futures/orderbook/large-limit-order/history",
        "v4_futures_orderbook_depth_history": "/api/futures/orderbook/depth/history",
        "v4_futures_taker_buy_sell_volume_history": "/api/futures/taker-buy-sell-volume/history",
        "v4_futures_taker_buy_sell_coin_volume_history": "/api/futures/taker-buy-sell-volume/coin-history",
        "v4_futures_basis_history": "/api/futures/basis/history",
        
        # V4 API端点 - 现货
        "v4_spot_coins_markets": "/api/spot/coins-markets",
        "v4_spot_price_history": "/api/spot/price/history",
        "v4_spot_orderbook_large_limit_order": "/api/spot/orderbook/large-limit-order",
        "v4_spot_orderbook_large_limit_order_history": "/api/spot/orderbook/large-limit-order/history",
        "v4_spot_orderbook_depth_history": "/api/spot/orderbook/depth/history",
        "v4_spot_taker_buy_sell_volume_history": "/api/spot/taker-buy-sell-volume/history",
        "v4_spot_taker_buy_sell_coin_volume_history": "/api/spot/taker-buy-sell-volume/coin-history",
        
        # Legacy API端点
        "legacy_funding_rate": "/api/futures/fundingRate",
        "legacy_liquidation": "/api/futures/liquidation",
        "legacy_futures_flow": "/api/futures/flow",
        "legacy_market_money_flow": "/api/futures/marketMoneyFlow"
    },
    "rate_limit": {
        "requests_per_minute": 60,
        "delay_between_requests": 1.0
    },
    "timeout": 30,
    "max_retries": 3
}

# Google Gemini AI配置
GOOGLE_AI_CONFIG = {
    "base_url": "https://generativelanguage.googleapis.com/v1beta/models",
    "model": "gemini-2.5-flash",
    "api_key": os.getenv('GOOGLE_AI_API_KEY', 'AIzaSyBt4pIYmLYheuMpXSCj5VLkCA-fhfdEVT4'),
    "backup_api_keys": [
        "AIzaSyBt4pIYmLYheuMpXSCj5VLkCA-fhfdEVT4",
        "AIzaSyBSllSwrObqvUiXqFG5RUJXB6woZoBSaTk", 
        "AIzaSyB1PiY-qfnM8Yj8kctuLhSPW8ckUJYu5-U",
        "AIzaSyB3YP05qliWlJnmL6w07rMRT7BUpl_dM94"
    ],
    "endpoint": "generateContent",
    "timeout": 300,
    "max_retries": 6
}

# ========== 数据存储配置 ==========

# 数据存储结构
DATA_STORAGE_CONFIG = {
    "root_dir": DATA_STORAGE_ROOT,
    "cache_ttl_seconds": 3600,  # 1小时缓存
    "max_files_per_dir": 0,  # 0表示不限制文件数量（禁用清理）
    "cleanup_older_than_days": 0,  # 0表示不清理旧文件（禁用清理）
    "file_naming_pattern": "{timestamp}_{api_name}.json",
    "enable_cleanup": False,  # 禁用自动清理功能
    "show_file_stats": True   # 显示文件统计信息
}

# 调度器配置
SCHEDULER_CONFIG = {
    "enable_scheduler": True,  # 启用调度器
    "interval_seconds": 60,    # 全局默认执行间隔（秒）- 默认60秒（1分钟）
    "run_on_startup": True,    # 启动时立即执行一次
    "max_concurrent_jobs": 1,  # 最大并发任务数（防止重叠执行）
    "retry_failed_jobs": True, # 重试失败的任务
    "log_level": "INFO",       # 调度器日志级别
    "use_individual_intervals": True,  # 启用独立间隔管理
    "check_interval": 10       # 调度器检查间隔（秒）
}

# 各API请求器独立间隔配置（秒）
API_REQUESTER_INTERVALS = {
    # 币安API - 高频更新
    "binance_get_futures_exchange_info": 60,      # 5分钟 - 交易所信息变化较少
    "binance_get_futures_premium_index": 60,       # 1分钟 - 资金费率需要实时监控

    # 币安订单薄 - 高频更新
    "binance_orderbook_futures": 30,               # 30秒 - 合约订单薄
    "binance_orderbook_spot": 30,                  # 30秒 - 现货订单薄

    # CoinGlass市场数据 - 中频更新
    "coinglass_get_v4_futures_binance_markets": 60,   # 2分钟 - 币安合约市场数据
    "coinglass_get_v4_futures_all_markets": 60,       # 3分钟 - 全市场合约数据
    "coinglass_get_v4_spot_all_markets": 60,          # 3分钟 - 全市场现货数据

    # CoinGlass指标数据 - 低频更新
    "coinglass_get_rsi_list": 60,                 # 1分钟 - RSI指标
    "coinglass_get_cgdi_index": 86400,             # 24小时 - CGDI指数（日更新）
    "coinglass_get_cdri_index": 86400,             # 24小时 - CDRI指数（日更新）
    "coinglass_get_bitcoin_s2f": 86400,            # 24小时 - S2F模型（日更新）
    "coinglass_get_golden_ratio": 86400,           # 24小时 - 黄金比例乘数（日更新）
    "coinglass_get_fear_greed_index": 86400,       # 24小时 - 恐惧贪婪指数（日更新）

    # Google AI - 按需更新
    "google_ai_generate_gemini_content": 86400,    # 24小时 - AI内容生成（按需）
}

# 确保数据存储目录存在
def ensure_data_storage_directories():
    """确保所有必要的数据存储目录存在（按API来源分组）"""
    # 按API来源组织的目录结构
    api_providers = {
        "binance": [
            "binance_get_futures_exchange_info",
            "binance_get_futures_premium_index"
        ],
        "coinglass": [
            "coinglass_get_v4_futures_binance_markets",
            "coinglass_get_v4_futures_all_markets",
            "coinglass_get_v4_spot_all_markets"
        ],
        "google_ai": [
            "google_ai_generate_gemini_content"
        ]
    }

    # 创建按API来源分组的目录结构
    for provider, api_dirs in api_providers.items():
        # 创建API来源的主目录
        provider_path = DATA_STORAGE_ROOT / provider
        provider_path.mkdir(parents=True, exist_ok=True)

        # 创建每个API的子目录
        for api_dir in api_dirs:
            api_path = provider_path / api_dir
            api_path.mkdir(parents=True, exist_ok=True)

    # 创建.gitkeep文件
    gitkeep_path = DATA_STORAGE_ROOT / ".gitkeep"
    if not gitkeep_path.exists():
        gitkeep_path.touch()

# ========== 其他配置 ==========

# 用户代理
USER_AGENT = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"

# HTTP会话配置
HTTP_SESSION_CONFIG = {
    "pool_connections": 10,
    "pool_maxsize": 20, 
    "max_retries": 0,
    "pool_block": False
}

# 初始化数据存储目录
if __name__ == "__main__":
    ensure_data_storage_directories()
    print("✅ 数据存储目录初始化完成") 