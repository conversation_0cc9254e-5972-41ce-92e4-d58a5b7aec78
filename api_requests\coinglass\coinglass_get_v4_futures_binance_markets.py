#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
CoinGlass V4 合约市场数据API请求器（仅币安）
分页获取币安合约市场的综合数据
"""

import sys
import time
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from main import FileManager, HTTPClient, setup_logger
from config import COINGLASS_CONFIG

class CoinglassV4FuturesBinanceMarketsRequester:
    """CoinGlass V4 合约市场数据请求器（仅币安）"""

    def __init__(self):
        """初始化请求器"""
        self.api_identifier = "coinglass_get_v4_futures_binance_markets"
        self.logger = setup_logger(f"api_requests.{self.api_identifier}")
        self.file_manager = FileManager()
        
        # 创建HTTP客户端
        self.http_client = HTTPClient(
            timeout=COINGLASS_CONFIG["timeout"],
            max_retries=COINGLASS_CONFIG["max_retries"]
        )
        
        # 设置CoinGlass API认证头
        self.http_client.add_auth_header("CG-API-KEY", COINGLASS_CONFIG["api_key"])
        
        self.logger.info(f"✅ {self.api_identifier} 请求器初始化完成")
    
    def fetch_data(self, exchange_list: str = "Binance", per_page: int = 100) -> bool:
        """
        获取数据并存储（支持分页获取所有数据）
        
        Args:
            exchange_list: 交易所列表，默认只获取Binance
            per_page: 每页数据量，最大100
        """
        try:
            self.logger.info("🔄 开始获取CoinGlass V4合约市场数据...")
            
            # 构建请求URL
            base_url = COINGLASS_CONFIG["base_url"]
            endpoint = COINGLASS_CONFIG["endpoints"]["v4_futures_coins_markets"]
            url = f"{base_url}{endpoint}"
            
            all_data = []
            page = 1
            total_pages = None
            
            while True:
                self.logger.info(f"📄 获取第{page}页数据...")
                
                # 构建查询参数
                params = {
                    "page": page,
                    "per_page": min(per_page, 100),  # 确保不超过最大限制
                    "exchange_list": exchange_list
                }
                
                # 发送请求
                success, data, message = self.http_client.get(url, params=params)
                
                if not success:
                    self.logger.error(f"❌ API请求失败 (第{page}页): {message}")
                    return False
                
                # 验证响应数据格式
                if not isinstance(data, dict) or data.get("code") != "0":
                    error_msg = data.get("msg", "未知错误") if isinstance(data, dict) else "响应格式错误"
                    self.logger.error(f"❌ API返回错误 (第{page}页): {error_msg}")
                    return False
                
                # 获取当前页数据
                page_data = data.get("data", [])
                if not page_data:
                    self.logger.info(f"📄 第{page}页没有更多数据，结束获取")
                    break
                
                all_data.extend(page_data)
                self.logger.info(f"✅ 第{page}页获取成功: {len(page_data)}条记录")
                
                # 检查是否还有更多页面（根据返回数据量判断）
                if len(page_data) < per_page:
                    self.logger.info("📄 已获取所有页面数据")
                    break
                
                page += 1
                
                # 避免请求过快
                delay = COINGLASS_CONFIG["rate_limit"]["delay_between_requests"]
                if delay > 0:
                    time.sleep(delay)
            
            if not all_data:
                self.logger.error("❌ 没有获取到任何数据")
                return False
            
            # 统计信息
            unique_symbols = set(item.get("symbol", "") for item in all_data)
            
            # 提取关键信息作为元数据
            metadata = {
                "exchange_list": exchange_list,
                "total_records": len(all_data),
                "total_pages": page - 1,
                "unique_symbols": len(unique_symbols),
                "per_page": per_page,
                "request_url": url
            }
            
            # 保存数据
            save_success, file_path, save_message = self.file_manager.save_api_data(
                api_identifier=self.api_identifier,
                data=all_data,
                metadata=metadata
            )
            
            if save_success:
                self.logger.info(f"✅ 数据获取并保存成功: {save_message}")
                self.logger.info(f"📊 总记录数: {len(all_data)}")
                self.logger.info(f"📄 总页数: {page - 1}")
                self.logger.info(f"🪙 唯一币种: {len(unique_symbols)}")
                return True
            else:
                self.logger.error(f"❌ 数据保存失败: {save_message}")
                return False
                
        except Exception as e:
            self.logger.error(f"❌ 获取数据时发生错误: {str(e)}")
            return False
        finally:
            self.http_client.close()
    
    def get_latest_data(self):
        """获取最新缓存的数据"""
        success, data, message = self.file_manager.load_latest_api_data(self.api_identifier)
        if success:
            self.logger.info(f"✅ 已加载最新数据: {message}")
            return data
        else:
            self.logger.error(f"❌ 加载数据失败: {message}")
            return None
    
    def is_data_fresh(self, max_age_seconds: int = 600) -> bool:
        """检查数据是否新鲜（默认10分钟）"""
        return self.file_manager.is_data_fresh(self.api_identifier, max_age_seconds)

def main():
    """主函数 - 可以直接运行此脚本"""
    requester = CoinglassV4FuturesBinanceMarketsRequester()

    # 获取Binance交易所的合约市场数据
    success = requester.fetch_data(exchange_list="Binance")

    if success:
        print("CoinGlass V4合约市场数据（币安）获取成功")
        exit(0)
    else:
        print("CoinGlass V4合约市场数据（币安）获取失败")
        exit(1)

if __name__ == "__main__":
    main() 