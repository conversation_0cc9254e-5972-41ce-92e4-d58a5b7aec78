#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
币安合约资金费率和溢价指数请求器
获取永续合约资金费率和标记价格信息
"""

import sys
from pathlib import Path
from typing import Dict, Any, Optional

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from main import FileManager, HTTPClient, setup_logger
from config import BINANCE_CONFIG

class BinanceFuturesPremiumIndexRequester:
    """币安合约资金费率和溢价指数请求器"""
    
    def __init__(self):
        self.api_identifier = "binance_get_futures_premium_index"
        self.logger = setup_logger(f"api_requests.{self.api_identifier}")
        self.file_manager = FileManager()
        self.http_client = HTTPClient()
        
        # API配置
        self.base_url = BINANCE_CONFIG["futures_base_url"]
        self.endpoint = BINANCE_CONFIG["endpoints"]["futures_premium_index"]
        self.timeout = BINANCE_CONFIG.get("timeout", 30)
        self.max_retries = BINANCE_CONFIG.get("max_retries", 3)
    
    def fetch_data(self, symbol: Optional[str] = None) -> bool:
        """
        获取资金费率和溢价指数数据
        
        Args:
            symbol: 可选，指定交易对符号
            
        Returns:
            bool: 是否成功获取数据
        """
        try:
            self.logger.info(f"🚀 开始获取币安合约资金费率和溢价指数数据...")
            
            # 构建请求URL
            url = f"{self.base_url}{self.endpoint}"
            
            # 构建请求参数
            params = {}
            if symbol:
                params["symbol"] = symbol
            
            # 发送请求
            success, response, message = self.http_client.get(
                url=url,
                params=params,
                timeout=self.timeout
            )
            
            if not success or not response:
                self.logger.error(f"❌ 获取数据失败：{message}")
                return False
            
            # 处理响应数据
            if isinstance(response, list):
                data_list = response
            else:
                data_list = [response]  # 单个交易对的情况
            
            # 计算统计信息
            total_symbols = len(data_list)
            
            # 构建元数据
            metadata = {
                "request_url": url,
                "request_params": params,
                "symbols_count": total_symbols,
                "data_type": "premium_index_and_funding_rate",
                "symbol_filter": symbol if symbol else "all"
            }
            
            # 保存数据
            success = self.file_manager.save_api_data(
                api_identifier=self.api_identifier,
                data=data_list,
                metadata=metadata
            )
            
            if success:
                self.logger.info(f"✅ 成功获取并保存 {total_symbols} 个交易对的资金费率和溢价指数数据")
                return True
            else:
                self.logger.error("❌ 保存数据失败")
                return False
                
        except Exception as e:
            self.logger.error(f"❌ 获取币安合约资金费率和溢价指数数据失败: {e}")
            return False
    
    def get_latest_data(self):
        """获取最新缓存的数据"""
        success, data, message = self.file_manager.load_latest_api_data(self.api_identifier)
        if success:
            self.logger.info(f"✅ 已加载最新数据: {message}")
            return data
        else:
            self.logger.error(f"❌ 加载数据失败: {message}")
            return None
    
    def is_data_fresh(self, max_age_seconds: int = 600) -> bool:
        """检查数据是否新鲜（默认10分钟）"""
        return self.file_manager.is_data_fresh(self.api_identifier, max_age_seconds)

def main():
    """主函数 - 可以直接运行此脚本"""
    requester = BinanceFuturesPremiumIndexRequester()
    
    # 获取所有交易对的资金费率和溢价指数数据
    success = requester.fetch_data()
    
    if success:
        print("币安合约资金费率和溢价指数数据获取成功")
        exit(0)
    else:
        print("币安合约资金费率和溢价指数数据获取失败")
        exit(1)

if __name__ == "__main__":
    main()
