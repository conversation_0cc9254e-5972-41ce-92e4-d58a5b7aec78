#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试订单薄和RSI数据模块
测试新增的币安订单薄数据获取和CoinGlass RSI数据获取功能
"""

import sys
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from api_requests.binance.binance_orderbook_requester import BinanceOrderbookRequester
from api_requests.coinglass.coinglass_get_rsi_list import CoinglassRSIListRequester

def test_binance_orderbook():
    """测试币安订单薄数据获取"""
    print("=" * 50)
    print("🔍 测试币安订单薄数据获取")
    print("=" * 50)
    
    try:
        requester = BinanceOrderbookRequester()
        
        # 测试合约订单薄
        print("\n1. 📊 获取BTCUSDT合约订单薄...")
        success1 = requester.fetch_futures_orderbook("BTCUSDT", 100)
        print(f"   合约订单薄获取: {'✅ 成功' if success1 else '❌ 失败'}")
        
        # 测试现货订单薄
        print("\n2. 📊 获取BTCUSDT现货订单薄...")
        success2 = requester.fetch_spot_orderbook("BTCUSDT", 100)
        print(f"   现货订单薄获取: {'✅ 成功' if success2 else '❌ 失败'}")
        
        # 测试批量获取
        print("\n3. 📊 批量获取订单薄数据...")
        symbols = ["BTCUSDT", "ETHUSDT"]
        results = requester.fetch_multiple_orderbooks(symbols, "futures", 50)
        success_count = sum(1 for success in results.values() if success)
        print(f"   批量获取: {success_count}/{len(symbols)} 成功")
        
        # 测试数据加载
        print("\n4. 📊 测试数据加载...")
        data = requester.get_latest_orderbook_data("BTCUSDT", "futures")
        if data:
            bids_count = len(data.get('bids', []))
            asks_count = len(data.get('asks', []))
            print(f"   ✅ 成功加载数据 (买单:{bids_count}, 卖单:{asks_count})")
        else:
            print("   ❌ 数据加载失败")
        
        # 测试数据新鲜度
        print("\n5. 📊 测试数据新鲜度...")
        is_fresh = requester.is_orderbook_data_fresh("BTCUSDT", "futures", 300)
        print(f"   数据新鲜度: {'✅ 新鲜' if is_fresh else '❌ 过期'}")

        # 测试获取所有币种数据
        print("\n6. 📊 测试获取所有币种数据...")
        all_futures_data = requester.get_all_symbols_data("futures", 600)
        print(f"   合约币种数据: {len(all_futures_data)} 个币种")

        # 测试获取币种列表
        print("\n7. 📊 测试获取币种列表...")
        futures_symbols = requester.get_symbols_list("futures")
        spot_symbols = requester.get_symbols_list("spot")
        print(f"   合约币种列表: {len(futures_symbols)} 个")
        print(f"   现货币种列表: {len(spot_symbols)} 个")
        if futures_symbols:
            print(f"   合约币种示例: {futures_symbols[:3]}")
        if spot_symbols:
            print(f"   现货币种示例: {spot_symbols[:3]}")

        return success1 and success2 and (success_count > 0)
        
    except Exception as e:
        print(f"❌ 币安订单薄测试失败: {e}")
        return False

def test_coinglass_rsi():
    """测试CoinGlass RSI数据获取"""
    print("\n" + "=" * 50)
    print("🔍 测试CoinGlass RSI数据获取")
    print("=" * 50)
    
    try:
        requester = CoinglassRSIListRequester()
        
        # 测试获取RSI列表
        print("\n1. 📊 获取RSI列表数据...")
        success = requester.get_rsi_data()
        print(f"   RSI列表获取: {'✅ 成功' if success else '❌ 失败'}")
        
        if success:
            # 测试数据加载
            print("\n2. 📊 测试数据加载...")
            success_load, data, message = requester.file_manager.load_latest_api_data(requester.api_identifier)
            if success_load and data:
                rsi_data = data.get('data', [])
                print(f"   ✅ 成功加载 {len(rsi_data)} 个币种的RSI数据")
                
                # 显示前几个币种的RSI信息
                if rsi_data:
                    print("\n   📈 前3个币种的RSI信息:")
                    for i, coin_rsi in enumerate(rsi_data[:3]):
                        symbol = coin_rsi.get('symbol', 'Unknown')
                        rsi_1h = coin_rsi.get('rsi1h', 'N/A')
                        rsi_4h = coin_rsi.get('rsi4h', 'N/A')
                        rsi_24h = coin_rsi.get('rsi24h', 'N/A')
                        print(f"      {i+1}. {symbol}: 1h={rsi_1h}, 4h={rsi_4h}, 24h={rsi_24h}")
            else:
                print(f"   ❌ 数据加载失败: {message}")
        
        return success
        
    except Exception as e:
        print(f"❌ CoinGlass RSI测试失败: {e}")
        return False

def test_data_freshness():
    """测试数据新鲜度检查"""
    print("\n" + "=" * 50)
    print("🔍 测试数据新鲜度检查")
    print("=" * 50)
    
    try:
        # 测试订单薄数据新鲜度
        orderbook_requester = BinanceOrderbookRequester()
        rsi_requester = CoinglassRSIListRequester()
        
        print("\n1. 📊 检查订单薄数据新鲜度...")
        is_fresh_orderbook = orderbook_requester.is_orderbook_data_fresh("BTCUSDT", "futures", 60)
        print(f"   BTCUSDT合约订单薄数据: {'✅ 新鲜(1分钟内)' if is_fresh_orderbook else '❌ 过期'}")
        
        print("\n2. 📊 检查RSI数据新鲜度...")
        is_fresh_rsi = rsi_requester.file_manager.is_data_fresh(rsi_requester.api_identifier, 300)
        print(f"   RSI数据: {'✅ 新鲜(5分钟内)' if is_fresh_rsi else '❌ 过期'}")
        
        return True
        
    except Exception as e:
        print(f"❌ 数据新鲜度测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始测试订单薄和RSI数据模块")
    print("=" * 60)
    
    # 运行所有测试
    test_results = []
    
    # 测试币安订单薄
    result1 = test_binance_orderbook()
    test_results.append(("币安订单薄", result1))
    
    # 测试CoinGlass RSI
    result2 = test_coinglass_rsi()
    test_results.append(("CoinGlass RSI", result2))
    
    # 测试数据新鲜度
    result3 = test_data_freshness()
    test_results.append(("数据新鲜度", result3))
    
    # 输出测试结果
    print("\n" + "=" * 60)
    print("📊 测试结果汇总")
    print("=" * 60)
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name:20} : {status}")
    
    # 计算总体结果
    success_count = sum(1 for _, result in test_results if result)
    total_tests = len(test_results)
    
    print(f"\n总体结果: {success_count}/{total_tests} 测试通过")
    
    if success_count == total_tests:
        print("🎉 所有测试通过！新增模块工作正常。")
        return 0
    else:
        print("⚠️  部分测试失败，请检查相关配置和网络连接。")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
