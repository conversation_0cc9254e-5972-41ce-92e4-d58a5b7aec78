#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
币安订单簿数据获取器 - 清理版本
支持全量活跃币种获取和手动配置模式
"""

import sys
import time
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple

# 添加项目路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from config import BINANCE_CONFIG, BINANCE_ORDERBOOK_CONFIG
from utils.file_manager import FileManager
from utils.http_client import HTTPClient
from utils.logger import setup_logger


class BinanceOrderbookRequester:
    """币安订单簿数据获取器"""
    
    def __init__(self):
        """初始化"""
        # 设置日志
        self.logger = setup_logger("api_requests.binance_orderbook")
        
        # 初始化文件管理器
        self.file_manager = FileManager()
        
        # 初始化HTTP客户端
        self.http_client = HTTPClient()
        
        # 缓存
        self._futures_symbols_cache = None
        self._spot_symbols_cache = None
        self._cache_timestamp = 0
        self._cache_duration = 300  # 5分钟缓存
        
        # 被阻止的交易对
        self.blocked_symbols = {'BNXUSDT', 'ALPACAUSDT'}
        
        self.logger.info("✅ binance_orderbook 请求器初始化完成")
    
    def get_active_futures_symbols(self, force_refresh: bool = False, max_symbols: int = 500) -> List[str]:
        """
        获取活跃的USDT合约交易对
        
        Args:
            force_refresh: 是否强制刷新缓存
            max_symbols: 最大返回数量
            
        Returns:
            List[str]: 按交易量排序的活跃交易对列表
        """
        try:
            current_time = time.time()
            
            # 检查缓存
            if (not force_refresh and 
                self._futures_symbols_cache and 
                current_time - self._cache_timestamp < self._cache_duration):
                return self._futures_symbols_cache[:max_symbols]
            
            self.logger.info("🔄 开始获取活跃合约交易对...")
            
            # 获取交易所信息
            exchange_info_url = f"{BINANCE_CONFIG['futures_base_url']}/fapi/v1/exchangeInfo"
            exchange_response = self.http_client.get(exchange_info_url)
            
            if not exchange_response:
                self.logger.error("❌ 获取合约交易所信息失败")
                return []
            
            # 筛选活跃的USDT永续合约
            active_symbols = []
            for symbol_info in exchange_response.get('symbols', []):
                if (symbol_info.get('status') == 'TRADING' and
                    symbol_info.get('quoteAsset') == 'USDT' and
                    symbol_info.get('contractType') == 'PERPETUAL' and
                    symbol_info.get('symbol') not in self.blocked_symbols):
                    active_symbols.append(symbol_info['symbol'])
            
            self.logger.info(f"📊 找到 {len(active_symbols)} 个活跃合约交易对")
            
            # 获取24小时交易数据进行排序
            ticker_url = f"{BINANCE_CONFIG['futures_base_url']}/fapi/v1/ticker/24hr"
            ticker_response = self.http_client.get(ticker_url)
            
            if ticker_response:
                # 创建交易量映射
                volume_map = {}
                for ticker in ticker_response:
                    symbol = ticker.get('symbol')
                    if symbol in active_symbols:
                        volume_map[symbol] = float(ticker.get('quoteVolume', 0))
                
                # 按交易量排序
                active_symbols.sort(key=lambda x: volume_map.get(x, 0), reverse=True)
                
                top_10 = active_symbols[:10]
                self.logger.info(f"✅ 按交易量排序完成，前10名: {top_10}")
            
            # 更新缓存
            self._futures_symbols_cache = active_symbols
            self._cache_timestamp = current_time
            
            self.logger.info(f"✅ 获取到 {len(active_symbols)} 个活跃合约交易对（已按交易量排序）")
            return active_symbols[:max_symbols]
            
        except Exception as e:
            self.logger.error(f"❌ 获取活跃合约交易对失败: {e}")
            return []
    
    def get_active_spot_symbols(self, force_refresh: bool = False, max_symbols: int = 500) -> List[str]:
        """
        获取活跃的USDT现货交易对
        
        Args:
            force_refresh: 是否强制刷新缓存
            max_symbols: 最大返回数量
            
        Returns:
            List[str]: 按交易量排序的活跃交易对列表
        """
        try:
            current_time = time.time()
            
            # 检查缓存
            if (not force_refresh and 
                self._spot_symbols_cache and 
                current_time - self._cache_timestamp < self._cache_duration):
                return self._spot_symbols_cache[:max_symbols]
            
            self.logger.info("🔄 开始获取活跃现货交易对...")
            
            # 获取交易所信息
            exchange_info_url = f"{BINANCE_CONFIG['spot_base_url']}/api/v3/exchangeInfo"
            exchange_response = self.http_client.get(exchange_info_url)
            
            if not exchange_response:
                self.logger.error("❌ 获取现货交易所信息失败")
                return []
            
            # 筛选活跃的USDT交易对
            active_symbols = []
            for symbol_info in exchange_response.get('symbols', []):
                if (symbol_info.get('status') == 'TRADING' and
                    symbol_info.get('quoteAsset') == 'USDT' and
                    symbol_info.get('symbol') not in self.blocked_symbols):
                    active_symbols.append(symbol_info['symbol'])
            
            self.logger.info(f"📊 找到 {len(active_symbols)} 个活跃现货交易对")
            
            # 获取24小时交易数据进行排序
            ticker_url = f"{BINANCE_CONFIG['spot_base_url']}/api/v3/ticker/24hr"
            ticker_response = self.http_client.get(ticker_url)
            
            if ticker_response:
                # 创建交易量映射
                volume_map = {}
                for ticker in ticker_response:
                    symbol = ticker.get('symbol')
                    if symbol in active_symbols:
                        volume_map[symbol] = float(ticker.get('quoteVolume', 0))
                
                # 按交易量排序
                active_symbols.sort(key=lambda x: volume_map.get(x, 0), reverse=True)
                
                top_10 = active_symbols[:10]
                self.logger.info(f"✅ 按交易量排序完成，前10名: {top_10}")
            
            # 更新缓存
            self._spot_symbols_cache = active_symbols
            self._cache_timestamp = current_time
            
            self.logger.info(f"✅ 获取到 {len(active_symbols)} 个活跃现货交易对（已按交易量排序）")
            return active_symbols[:max_symbols]
            
        except Exception as e:
            self.logger.error(f"❌ 获取活跃现货交易对失败: {e}")
            return []


if __name__ == "__main__":
    # 简单测试
    requester = BinanceOrderbookRequester()
    
    print("测试获取活跃合约交易对...")
    futures_symbols = requester.get_active_futures_symbols(max_symbols=10)
    print(f"前10个合约: {futures_symbols}")
    
    print("\n测试获取活跃现货交易对...")
    spot_symbols = requester.get_active_spot_symbols(max_symbols=10)
    print(f"前10个现货: {spot_symbols}")
