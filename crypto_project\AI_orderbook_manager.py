"""
AI订单薄数据管理器
提供CoinGlass API的订单薄相关数据获取功能
包括：大额订单薄、大额订单薄历史、币种聚合挂单深度历史
"""

import requests
import time
import json
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple

class AIOrderbookManager:
    """AI订单薄数据管理器"""
    
    def __init__(self):
        self.base_url = "https://open-api-v4.coinglass.com/api"
        self.api_key = "c40fbbee201d4dfab3a4b62f37f0b610"
        self.headers = {
            "accept": "application/json",
            "CG-API-KEY": self.api_key
        }
        self.cache = {}
        self.cache_ttl = 300  # 5分钟缓存
        
        # 设置日志
        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger(__name__)
    
    def _make_request(self, endpoint: str, params: dict) -> Optional[dict]:
        """发起API请求"""
        try:
            url = f"{self.base_url}{endpoint}"
            self.logger.info(f"请求订单薄数据: {url} with params: {params}")
            
            response = requests.get(url, headers=self.headers, params=params, timeout=30)
            response.raise_for_status()
            
            data = response.json()
            if data.get('code') == '0':
                return data.get('data')
            else:
                self.logger.error(f"API返回错误: {data.get('msg')}")
                return None
                
        except Exception as e:
            self.logger.error(f"订单薄API请求失败: {str(e)}")
            return None
    
    def _get_cache_key(self, method: str, **kwargs) -> str:
        """生成缓存键"""
        key_parts = [method]
        for k, v in sorted(kwargs.items()):
            key_parts.append(f"{k}_{v}")
        return "_".join(key_parts)
    
    def _is_cache_valid(self, cache_key: str) -> bool:
        """检查缓存是否有效"""
        if cache_key not in self.cache:
            return False
        
        cache_time = self.cache[cache_key].get('timestamp', 0)
        return time.time() - cache_time < self.cache_ttl
    
    def get_large_limit_orders(self, exchange: str = "Binance", symbol: str = "BTCUSDT") -> Optional[List[dict]]:
        """
        获取大额订单薄数据（当前）
        阈值: BTC ≥ 100万, ETH ≥ 50万, Other ≥ 5万
        """
        cache_key = self._get_cache_key("large_orders", exchange=exchange, symbol=symbol)
        
        if self._is_cache_valid(cache_key):
            return self.cache[cache_key]['data']
        
        params = {
            'exchange': exchange,
            'symbol': symbol
        }
        
        endpoint = "/futures/orderbook/large-limit-order"
        data = self._make_request(endpoint, params)
        
        if data:
            self.cache[cache_key] = {
                'data': data,
                'timestamp': time.time()
            }
            return data
        
        return None
    
    def get_large_limit_orders_history(self, exchange: str = "Binance", symbol: str = "BTCUSDT", 
                                     state: int = 1, hours_back: int = 24) -> Optional[List[dict]]:
        """
        获取大额订单薄历史数据
        state: 1-进行中, 2-已完成, 3-已撤销
        """
        end_time = int(time.time() * 1000)
        start_time = end_time - (hours_back * 60 * 60 * 1000)
        
        cache_key = self._get_cache_key("large_orders_history", 
                                      exchange=exchange, symbol=symbol, 
                                      state=state, start_time=start_time)
        
        if self._is_cache_valid(cache_key):
            return self.cache[cache_key]['data']
        
        params = {
            'exchange': exchange,
            'symbol': symbol,
            'state': state,
            'start_time': start_time,
            'end_time': end_time
        }
        
        endpoint = "/futures/orderbook/large-limit-order-history"
        data = self._make_request(endpoint, params)
        
        if data:
            self.cache[cache_key] = {
                'data': data,
                'timestamp': time.time()
            }
            return data
        
        return None
    
    def get_aggregated_orderbook_history(self, exchange_list: str = "Binance", 
                                       symbol: str = "BTC", interval: str = "1h", 
                                       depth_range: str = "1", limit: int = 100) -> Optional[List[dict]]:
        """
        获取币种聚合挂单深度历史数据
        depth_range: 深度百分比 (0.25、0.5、0.75、1、2、3、5、10)
        """
        cache_key = self._get_cache_key("aggregated_orderbook", 
                                      exchange_list=exchange_list, symbol=symbol, 
                                      interval=interval, depth_range=depth_range)
        
        if self._is_cache_valid(cache_key):
            return self.cache[cache_key]['data']
        
        params = {
            'exchange_list': exchange_list,
            'symbol': symbol,
            'interval': interval,
            'range': depth_range,
            'limit': limit
        }
        
        endpoint = "/futures/orderbook/aggregated-ask-bids-history"
        data = self._make_request(endpoint, params)
        
        if data:
            self.cache[cache_key] = {
                'data': data,
                'timestamp': time.time()
            }
            return data
        
        return None
    
    def analyze_large_orders(self, orders: List[dict]) -> Dict:
        """分析大额订单数据"""
        if not orders:
            return {}
        
        analysis = {
            'total_orders': len(orders),
            'buy_orders': 0,
            'sell_orders': 0,
            'total_buy_value': 0,
            'total_sell_value': 0,
            'avg_buy_price': 0,
            'avg_sell_price': 0,
            'largest_buy_order': 0,
            'largest_sell_order': 0,
            'active_orders': 0,
            'completed_orders': 0,
            'cancelled_orders': 0
        }
        
        buy_prices = []
        sell_prices = []
        
        for order in orders:
            # 订单方向统计
            if order.get('order_side') == 2:  # 买单
                analysis['buy_orders'] += 1
                analysis['total_buy_value'] += order.get('current_usd_value', 0)
                buy_prices.append(order.get('limit_price', 0))
                analysis['largest_buy_order'] = max(analysis['largest_buy_order'], 
                                                  order.get('current_usd_value', 0))
            elif order.get('order_side') == 1:  # 卖单
                analysis['sell_orders'] += 1
                analysis['total_sell_value'] += order.get('current_usd_value', 0)
                sell_prices.append(order.get('limit_price', 0))
                analysis['largest_sell_order'] = max(analysis['largest_sell_order'], 
                                                   order.get('current_usd_value', 0))
            
            # 订单状态统计
            state = order.get('order_state', 0)
            if state == 1:
                analysis['active_orders'] += 1
            elif state == 2:
                analysis['completed_orders'] += 1
            elif state == 3:
                analysis['cancelled_orders'] += 1
        
        # 计算平均价格
        if buy_prices:
            analysis['avg_buy_price'] = sum(buy_prices) / len(buy_prices)
        if sell_prices:
            analysis['avg_sell_price'] = sum(sell_prices) / len(sell_prices)
        
        return analysis
    
    def analyze_orderbook_depth(self, depth_data: List[dict]) -> Dict:
        """分析订单薄深度数据"""
        if not depth_data:
            return {}
        
        # 按时间排序
        sorted_data = sorted(depth_data, key=lambda x: x.get('time', 0))
        
        analysis = {
            'data_points': len(sorted_data),
            'latest_bids_usd': 0,
            'latest_asks_usd': 0,
            'latest_bids_quantity': 0,
            'latest_asks_quantity': 0,
            'avg_bids_usd': 0,
            'avg_asks_usd': 0,
            'bids_trend': 'stable',
            'asks_trend': 'stable',
            'bid_ask_ratio': 0,
            'liquidity_imbalance': 0
        }
        
        if sorted_data:
            # 最新数据
            latest = sorted_data[-1]
            analysis['latest_bids_usd'] = latest.get('aggregated_bids_usd', 0)
            analysis['latest_asks_usd'] = latest.get('aggregated_asks_usd', 0)
            analysis['latest_bids_quantity'] = latest.get('aggregated_bids_quantity', 0)
            analysis['latest_asks_quantity'] = latest.get('aggregated_asks_quantity', 0)
            
            # 计算平均值
            total_bids = sum(item.get('aggregated_bids_usd', 0) for item in sorted_data)
            total_asks = sum(item.get('aggregated_asks_usd', 0) for item in sorted_data)
            analysis['avg_bids_usd'] = total_bids / len(sorted_data)
            analysis['avg_asks_usd'] = total_asks / len(sorted_data)
            
            # 计算买卖比例
            if analysis['latest_asks_usd'] > 0:
                analysis['bid_ask_ratio'] = analysis['latest_bids_usd'] / analysis['latest_asks_usd']
            
            # 流动性失衡度 (正值表示买盘强, 负值表示卖盘强)
            total_liquidity = analysis['latest_bids_usd'] + analysis['latest_asks_usd']
            if total_liquidity > 0:
                analysis['liquidity_imbalance'] = (analysis['latest_bids_usd'] - analysis['latest_asks_usd']) / total_liquidity
            
            # 趋势分析 (比较最新和之前的数据)
            if len(sorted_data) >= 2:
                prev = sorted_data[-2]
                prev_bids = prev.get('aggregated_bids_usd', 0)
                prev_asks = prev.get('aggregated_asks_usd', 0)
                
                bids_change = (analysis['latest_bids_usd'] - prev_bids) / prev_bids if prev_bids > 0 else 0
                asks_change = (analysis['latest_asks_usd'] - prev_asks) / prev_asks if prev_asks > 0 else 0
                
                analysis['bids_trend'] = 'increasing' if bids_change > 0.05 else 'decreasing' if bids_change < -0.05 else 'stable'
                analysis['asks_trend'] = 'increasing' if asks_change > 0.05 else 'decreasing' if asks_change < -0.05 else 'stable'
        
        return analysis
    
    def get_comprehensive_orderbook_data(self, symbol: str = "BTCUSDT") -> Dict:
        """获取综合订单薄数据分析"""
        base_symbol = symbol.replace('USDT', '').replace('USD', '')
        
        # 获取各种订单薄数据
        large_orders = self.get_large_limit_orders(symbol=symbol)
        large_orders_history = self.get_large_limit_orders_history(symbol=symbol, hours_back=24)
        completed_orders = self.get_large_limit_orders_history(symbol=symbol, state=2, hours_back=24)
        depth_data = self.get_aggregated_orderbook_history(symbol=base_symbol, limit=24)
        
        # 分析数据
        large_orders_analysis = self.analyze_large_orders(large_orders or [])
        depth_analysis = self.analyze_orderbook_depth(depth_data or [])
        
        # 历史订单分析
        history_analysis = self.analyze_large_orders(large_orders_history or [])
        completed_analysis = self.analyze_large_orders(completed_orders or [])
        
        return {
            'symbol': symbol,
            'timestamp': int(time.time() * 1000),
            'current_large_orders': {
                'count': large_orders_analysis.get('total_orders', 0),
                'buy_orders': large_orders_analysis.get('buy_orders', 0),
                'sell_orders': large_orders_analysis.get('sell_orders', 0),
                'total_buy_value': large_orders_analysis.get('total_buy_value', 0),
                'total_sell_value': large_orders_analysis.get('total_sell_value', 0),
                'largest_buy_order': large_orders_analysis.get('largest_buy_order', 0),
                'largest_sell_order': large_orders_analysis.get('largest_sell_order', 0),
                'avg_buy_price': large_orders_analysis.get('avg_buy_price', 0),
                'avg_sell_price': large_orders_analysis.get('avg_sell_price', 0)
            },
            'orderbook_depth': {
                'latest_bids_usd': depth_analysis.get('latest_bids_usd', 0),
                'latest_asks_usd': depth_analysis.get('latest_asks_usd', 0),
                'latest_bids_quantity': depth_analysis.get('latest_bids_quantity', 0),
                'latest_asks_quantity': depth_analysis.get('latest_asks_quantity', 0),
                'bid_ask_ratio': depth_analysis.get('bid_ask_ratio', 0),
                'liquidity_imbalance': depth_analysis.get('liquidity_imbalance', 0),
                'bids_trend': depth_analysis.get('bids_trend', 'stable'),
                'asks_trend': depth_analysis.get('asks_trend', 'stable')
            },
            '24h_history': {
                'total_large_orders': history_analysis.get('total_orders', 0),
                'completed_orders': completed_analysis.get('total_orders', 0),
                'completion_rate': completed_analysis.get('total_orders', 0) / max(history_analysis.get('total_orders', 1), 1),
                'total_completed_value': completed_analysis.get('total_buy_value', 0) + completed_analysis.get('total_sell_value', 0)
            }
        }

def test_orderbook_manager():
    """测试订单薄管理器"""
    manager = AIOrderbookManager()
    
    print("=== 测试AI订单薄数据管理器 ===")
    
    # 测试获取大额订单薄
    print("\n1. 获取当前大额订单薄...")
    large_orders = manager.get_large_limit_orders()
    if large_orders:
        print(f"获取到 {len(large_orders)} 个大额订单")
    else:
        print("未获取到大额订单数据")
    
    # 测试获取聚合深度数据
    print("\n2. 获取聚合订单薄深度...")
    depth_data = manager.get_aggregated_orderbook_history()
    if depth_data:
        print(f"获取到 {len(depth_data)} 个深度数据点")
    else:
        print("未获取到深度数据")
    
    # 测试综合分析
    print("\n3. 获取综合订单薄分析...")
    comprehensive_data = manager.get_comprehensive_orderbook_data()
    print("综合分析结果:")
    print(json.dumps(comprehensive_data, indent=2, ensure_ascii=False))

if __name__ == "__main__":
    test_orderbook_manager() 