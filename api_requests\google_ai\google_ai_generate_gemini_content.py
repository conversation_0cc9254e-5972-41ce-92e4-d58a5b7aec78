#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Google AI Gemini 内容生成API请求器
调用Google Gemini API生成AI分析内容
"""

import sys
import json
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from main import FileManager, HTTPClient, setup_logger
from config import GOOGLE_AI_CONFIG

class GoogleAIGeminiContentRequester:
    """Google AI Gemini 内容生成请求器"""
    
    def __init__(self):
        """初始化请求器"""
        self.api_identifier = "google_ai_generate_gemini_content"
        self.logger = setup_logger(f"api_requests.{self.api_identifier}")
        self.file_manager = FileManager()
        
        # 创建HTTP客户端
        self.http_client = HTTPClient(
            timeout=GOOGLE_AI_CONFIG["timeout"],
            max_retries=GOOGLE_AI_CONFIG["max_retries"]
        )
        
        self.logger.info(f"✅ {self.api_identifier} 请求器初始化完成")
    
    def fetch_data(self, prompt: str, api_key: str = None) -> bool:
        """
        生成AI内容并存储
        
        Args:
            prompt: 输入提示词
            api_key: 可选的API密钥，如果不提供则使用配置中的默认密钥
        """
        try:
            self.logger.info("🔄 开始调用Google Gemini AI生成内容...")
            
            # 使用提供的API密钥或默认密钥
            current_api_key = api_key or GOOGLE_AI_CONFIG["api_key"]
            
            if not current_api_key:
                self.logger.error("❌ 未提供Google AI API密钥")
                return False
            
            # 构建请求URL
            base_url = GOOGLE_AI_CONFIG["base_url"]
            model = GOOGLE_AI_CONFIG["model"]
            endpoint = GOOGLE_AI_CONFIG["endpoint"]
            url = f"{base_url}/{model}:{endpoint}"
            
            # 设置认证头
            headers = {
                "X-goog-api-key": current_api_key,
                "Content-Type": "application/json"
            }
            
            # 构建请求体
            request_body = {
                "contents": [
                    {
                        "parts": [
                            {
                                "text": prompt
                            }
                        ]
                    }
                ]
            }
            
            self.logger.info(f"📝 提示词长度: {len(prompt)} 字符")
            
            # 发送POST请求
            success, data, message = self.http_client.post(
                url=url,
                json_data=request_body,
                headers=headers
            )
            
            if not success:
                self.logger.error(f"❌ API请求失败: {message}")
                return False
            
            # 验证响应数据
            if not isinstance(data, dict):
                self.logger.error("❌ 响应数据格式错误")
                return False
            
            # 解析Gemini API响应
            generated_content = ""
            token_count = 0
            
            if 'candidates' in data and len(data['candidates']) > 0:
                candidate = data['candidates'][0]
                if 'content' in candidate and 'parts' in candidate['content']:
                    if len(candidate['content']['parts']) > 0:
                        generated_content = candidate['content']['parts'][0].get('text', '')
                        
                # 获取token使用情况
                if 'usageMetadata' in data:
                    usage = data['usageMetadata']
                    token_count = usage.get('totalTokenCount', 0)
            
            if not generated_content:
                self.logger.error("❌ AI响应中没有生成的内容")
                return False
            
            # 提取关键信息作为元数据
            metadata = {
                "prompt_length": len(prompt),
                "response_length": len(generated_content),
                "token_count": token_count,
                "model": model,
                "api_key_used": current_api_key[:8] + "..." if current_api_key else None,
                "request_url": url
            }
            
            # 准备保存的数据
            save_data = {
                "prompt": prompt,
                "generated_content": generated_content,
                "raw_response": data
            }
            
            # 保存数据
            save_success, file_path, save_message = self.file_manager.save_api_data(
                api_identifier=self.api_identifier,
                data=save_data,
                metadata=metadata
            )
            
            if save_success:
                self.logger.info(f"✅ AI内容生成并保存成功: {save_message}")
                self.logger.info(f"📝 提示词长度: {metadata['prompt_length']} 字符")
                self.logger.info(f"📄 响应长度: {metadata['response_length']} 字符")
                self.logger.info(f"🔢 Token使用量: {metadata['token_count']}")
                return True
            else:
                self.logger.error(f"❌ 数据保存失败: {save_message}")
                return False
                
        except Exception as e:
            self.logger.error(f"❌ 生成AI内容时发生错误: {str(e)}")
            return False
        finally:
            self.http_client.close()
    
    def get_latest_data(self):
        """获取最新缓存的数据"""
        success, data, message = self.file_manager.load_latest_api_data(self.api_identifier)
        if success:
            self.logger.info(f"✅ 已加载最新数据: {message}")
            return data
        else:
            self.logger.error(f"❌ 加载数据失败: {message}")
            return None
    
    def is_data_fresh(self, max_age_seconds: int = 1800) -> bool:
        """检查数据是否新鲜（默认30分钟）"""
        return self.file_manager.is_data_fresh(self.api_identifier, max_age_seconds)

def main():
    """主函数 - 可以直接运行此脚本"""
    import argparse
    import sys

    parser = argparse.ArgumentParser(description='Google AI Gemini内容生成器')
    parser.add_argument('--prompt', type=str, help='输入提示词')
    parser.add_argument('--api-key', type=str, help='Google AI API密钥（可选）')
    parser.add_argument('--auto', action='store_true', help='自动模式（跳过执行）')

    args = parser.parse_args()

    # 自动模式下跳过执行
    if args.auto or not args.prompt:
        print("Google AI请求器需要提示词参数，自动模式下跳过")
        exit(0)

    requester = GoogleAIGeminiContentRequester()
    success = requester.fetch_data(prompt=args.prompt, api_key=args.api_key)

    if success:
        print("Google AI Gemini内容生成成功")
        exit(0)
    else:
        print("Google AI Gemini内容生成失败")
        exit(1)

if __name__ == "__main__":
    main() 