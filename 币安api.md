# Binance U本位合约行情接口 (REST API)

## 1. 测试服务器连通性 PING

**接口描述**
测试能否联通。

**HTTP请求**
`GET /fapi/v1/ping`

**请求权重**
1

**请求参数**
无

**响应示例**
```json
{}
```

---

## 2. 获取服务器时间

**接口描述**
获取服务器时间。

**HTTP请求**
`GET /fapi/v1/time`

**请求权重**
1

**请求参数**
无

**响应示例**
```json
{
  "serverTime": 1499827319559
}
```

---

## 3. 获取交易规则和交易对

**接口描述**
获取当前交易所的交易规则和交易对信息。

**HTTP请求**
`GET /fapi/v1/exchangeInfo`

**请求权重**
1

**请求参数**
无

**响应示例**
```json
{
	"exchangeFilters": [],
 	"rateLimits": [
 		{
 			"interval": "MINUTE",
   			"intervalNum": 1,
   			"limit": 2400,
   			"rateLimitType": "REQUEST_WEIGHT"
   		},
  		{
  			"interval": "MINUTE",
   			"intervalNum": 1,
   			"limit": 1200,
   			"rateLimitType": "ORDERS"
   		}
   	],
 	"serverTime": 1565613908500,
 	"assets": [
 		{
 			"asset": "BUSD",
   			"marginAvailable": true,
   			"autoAssetExchange": 0
   		},
 		{
 			"asset": "USDT",
   			"marginAvailable": true,
   			"autoAssetExchange": 0
   		}
   	],
 	"symbols": [
 		{
 			"symbol": "BLZUSDT",
 			"pair": "BLZUSDT",
 			"contractType": "PERPETUAL",
 			"deliveryDate": 4133404800000,
 			"onboardDate": 1598252400000,
 			"status": "TRADING",
 			"maintMarginPercent": "2.5000",
 			"requiredMarginPercent": "5.0000",
 			"baseAsset": "BLZ",
 			"quoteAsset": "USDT",
 			"marginAsset": "USDT",
 			"pricePrecision": 5,
 			"quantityPrecision": 0,
 			"baseAssetPrecision": 8,
 			"quotePrecision": 8,
 			"underlyingType": "COIN",
 			"underlyingSubType": ["STORAGE"],
 			"settlePlan": 0,
 			"triggerProtect": "0.15",
 			"filters": [
 				{
 					"filterType": "PRICE_FILTER",
     				"maxPrice": "300",
     				"minPrice": "0.0001",
     				"tickSize": "0.0001"
     			},
    			{
    				"filterType": "LOT_SIZE",
     				"maxQty": "10000000",
     				"minQty": "1",
     				"stepSize": "1"
     			},
    			{
    				"filterType": "MARKET_LOT_SIZE",
     				"maxQty": "590119",
     				"minQty": "1",
     				"stepSize": "1"
     			},
     			{
    				"filterType": "MAX_NUM_ORDERS",
    				"limit": 200
  				},
  				{
    				"filterType": "MAX_NUM_ALGO_ORDERS",
    				"limit": 10
  				},
  				{
  					"filterType": "MIN_NOTIONAL",
  					"notional": "5.0"
  				},
  				{
    				"filterType": "PERCENT_PRICE",
    				"multiplierUp": "1.1500",
    				"multiplierDown": "0.8500",
    				"multiplierDecimal": 4
    			}
   			],
 			"OrderType": [
   				"LIMIT",
   				"MARKET",
   				"STOP",
   				"STOP_MARKET",
   				"TAKE_PROFIT",
   				"TAKE_PROFIT_MARKET",
   				"TRAILING_STOP_MARKET"
   			],
   			"timeInForce": [
   				"GTC",
   				"IOC",
   				"FOK",
   				"GTX"
 			],
 			"liquidationFee": "0.010000",
   			"marketTakeBound": "0.30"
 		}
   	],
	"timezone": "UTC"
}```

---

## 4. 合约下架日期

**接口描述**
合约团队会在下架公告发布后更新 `GET /fapi/v1/exchangeInfo` 中 `deliveryDate` 为下架时间，请查询 获取交易规则和交易对 接口以提前获取合约交易对下架信息。

---

## 5. 深度信息

**接口描述**
获取交易对深度信息。

**HTTP请求**
`GET /fapi/v1/depth`

**请求权重**
| limit | 权重 |
| :--- | :-- |
| 5, 10, 20, 50 | 2 |
| 100 | 5 |
| 500 | 10 |
| 1000 | 20 |

**请求参数**
| 名称 | 类型 | 是否必需 | 描述 |
| :--- | :--- | :--- | :--- |
| symbol | STRING | YES | 交易对 |
| limit | INT | NO | 默认 500; 可选值: |

**响应示例**
```json
{
  "lastUpdateId": 1027024,
  "E": 1589436922972,
  "T": 1589436922959,
  "bids": [
    [
      "4.00000000",
      "431.00000000"
    ]
  ],
  "asks": [
    [
      "4.00000200",
      "12.00000000"
    ]
  ]
}
```

---

## 6. 近期成交

**接口描述**
获取近期订单簿成交。仅返回订单簿成交，即不会返回保险基金和自动减仓(ADL)成交。

**HTTP请求**
`GET /fapi/v1/trades`

**请求权重**
5

**请求参数**
| 名称 | 类型 | 是否必需 | 描述 |
| :--- | :--- | :--- | :--- |
| symbol | STRING | YES | 交易对 |
| limit | INT | NO | 默认:500，最大1000 |

**响应示例**
```json
[
  {
    "id": 28457,
    "price": "4.00000100",
    "qty": "12.00000000",
    "quoteQty": "48.00",
    "time": 1499865549590,
    "isBuyerMaker": true
  }
]
```

---

## 7. 查询历史成交 (MARKET_DATA)

**接口描述**
查询订单簿历史成交。仅返回订单簿成交，即不会返回保险基金和自动减仓(ADL)成交。仅支持返回最近3个月的数据。

**HTTP请求**
`GET /fapi/v1/historicalTrades`

**请求权重**
20

**请求参数**
| 名称 | 类型 | 是否必需 | 描述 |
| :--- | :--- | :--- | :--- |
| symbol | STRING | YES | 交易对 |
| limit | INT | NO | 默认值:500 最大值:1000 |
| fromId | LONG | NO | 从哪一条成交id开始返回. 缺省返回最近的成交记录 |

**响应示例**
```json
[
  {
    "id": 28457,
    "price": "4.00000100",
    "qty": "12.00000000",
    "quoteQty": "48.00",
    "time": 1499865549590,
    "isBuyerMaker": true
  }
]
```

---

## 8. 近期成交 (归集)

**接口描述**
归集交易与逐笔交易的区别在于，同一价格、同一方向、同一时间(100ms计算)的订单簿trade会被聚合为一条。
接口仅支持查询最近1年的交易数据。
如果同时发送startTime和endTime，间隔必须小于一小时。
如果没有发送任何筛选参数(fromId, startTime, endTime)，默认返回最近的成交记录。
保险基金和自动减仓(ADL)成交不属于订单簿成交，故不会被归并聚合。
同时发送startTime/endTime和fromId可能导致请求超时，建议仅发送fromId或仅发送startTime和endTime。

**HTTP请求**
`GET /fapi/v1/aggTrades`

**请求权重**
20

**请求参数**
| 名称 | 类型 | 是否必需 | 描述 |
| :--- | :--- | :--- | :--- |
| symbol | STRING | YES | 交易对 |
| fromId | LONG | NO | 从包含fromID的成交开始返回结果 |
| startTime | LONG | NO | 从该时刻之后的成交记录开始返回结果 |
| endTime | LONG | NO | 返回该时刻为止的成交记录 |
| limit | INT | NO | 默认 500; 最大 1000 |

**响应示例**
```json
[
  {
    "a": 26129,
    "p": "0.01633102",
    "q": "4.70443515",
    "f": 27781,
    "l": 27781,
    "T": 1498793709153,
    "m": true
  }
]
```

---

## 9. K线数据

**接口描述**
每根K线的开盘时间可视为唯一ID。缺省返回最近的数据。

**HTTP请求**
`GET /fapi/v1/klines`

**请求权重**
| LIMIT参数 | 权重 |
| :--- | :-- |
| [1,100) | 1 |
| [100, 500) | 2 |
| | 5 |
| > 1000 | 10 |

**请求参数**
| 名称 | 类型 | 是否必需 | 描述 |
| :--- | :--- | :--- | :--- |
| symbol | STRING | YES | 交易对 |
| interval | ENUM | YES | 时间间隔 |
| startTime | LONG | NO | 起始时间 |
| endTime | LONG | NO | 结束时间 |
| limit | INT | NO | 默认值:500 最大值:1500 |

**响应示例**
```json
[
  [
    1499040000000,
    "0.01634790",
    "0.80000000",
    "0.01575800",
    "0.01577100",
    "148976.11427815",
    1499644799999,
    "2434.19055334",
    308,
    "1756.87402397",
    "28.46694368",
    "17928899.62484339"
  ]
]
```

---

## 10. 连续合约 K 线数据

**接口描述**
每根K线的开盘时间可视为唯一ID。缺省返回最近的数据。

**HTTP请求**
`GET /fapi/v1/continuousKlines`

**请求权重**
| LIMIT参数 | 权重 |
| :--- | :-- |
| [1,100) | 1 |
| [100, 500) | 2 |
| | 5 |
| > 1000 | 10 |

**请求参数**
| 名称 | 类型 | 是否必需 | 描述 |
| :--- | :--- | :--- | :--- |
| pair | STRING | YES | 标的交易对 |
| contractType | ENUM | YES | 合约类型: PERPETUAL, CURRENT_QUARTER, NEXT_QUARTER |
| interval | ENUM | YES | 时间间隔 |
| startTime | LONG | NO | 起始时间 |
| endTime | LONG | NO | 结束时间 |
| limit | INT | NO | 默认值:500 最大值:1500 |

**响应示例**
```json
[
  [
    1607444700000,
    "18879.99",
    "18900.00",
    "18878.98",
    "18896.13",
    "492.363",
    1607444759999,
    "9302145.66080",
    1874,
    "385.983",
    "7292402.33267",
    "0"
  ]
]
```

---

## 11. 价格指数K线数据

**接口描述**
价格指数K线数据，每根K线的开盘时间可视为唯一ID。缺省返回最近的数据。

**HTTP请求**
`GET /fapi/v1/indexPriceKlines`

**请求权重**
| LIMIT参数 | 权重 |
| :--- | :-- |
| [1,100) | 1 |
| [100, 500) | 2 |
| | 5 |
| > 1000 | 10 |

**请求参数**
| 名称 | 类型 | 是否必需 | 描述 |
| :--- | :--- | :--- | :--- |
| pair | STRING | YES | 标的交易对 |
| interval | ENUM | YES | 时间间隔 |
| startTime | LONG | NO | 起始时间 |
| endTime | LONG | NO | 结束时间 |
| limit | INT | NO | 默认值:500 最大值:1500 |

**响应示例**
```json
[
  [
    1591256400000,
    "9653.69440000",
    "9653.69640000",
    "9651.38600000",
    "9651.55200000",
    "0",
    1591256459999,
    "0",
    60,
    "0",
    "0",
    "0"
  ]
]
```

---

## 12. 标记价格K线数据

**接口描述**
每根K线的开盘时间可视为唯一ID。缺省返回最近的数据。

**HTTP请求**
`GET /fapi/v1/markPriceKlines`

**请求权重**
| LIMIT参数 | 权重 |
| :--- | :-- |
| [1,100) | 1 |
| [100, 500) | 2 |
| | 5 |
| > 1000 | 10 |

**请求参数**
| 名称 | 类型 | 是否必需 | 描述 |
| :--- | :--- | :--- | :--- |
| symbol | STRING | YES | 交易对 |
| interval | ENUM | YES | 时间间隔 |
| startTime | LONG | NO | 起始时间 |
| endTime | LONG | NO | 结束时间 |
| limit | INT | NO | 默认值:500 最大值:1500 |

**响应示例**
```json
[
  [
    1591256400000,
    "9653.69440000",
    "9653.69640000",
    "9651.38600000",
    "9651.55200000",
    "0",
    1591256459999,
    "0",
    60,
    "0",
    "0",
    "0"
  ]
]
```

---

## 13. 溢价指数K线数据

**接口描述**
合约溢价指数K线。每根K线的开盘时间可视为唯一ID。缺省返回最近的数据。

**HTTP请求**
`GET /fapi/v1/premiumIndexKlines`

**请求权重**
| LIMIT参数 | 权重 |
| :--- | :-- |
| [1,100) | 1 |
| [100, 500) | 2 |
| | 5 |
| > 1000 | 10 |

**请求参数**
| 名称 | 类型 | 是否必需 | 描述 |
| :--- | :--- | :--- | :--- |
| symbol | STRING | YES | 交易对 |
| interval | ENUM | YES | 时间间隔 |
| startTime | LONG | NO | 起始时间 |
| endTime | LONG | NO | 结束时间 |
| limit | INT | NO | 默认值:500 最大值:1500 |

**响应示例**
```json
[
  [
    1691603820000,
    "-0.00042931",
    "-0.00023641",
    "-0.00059406",
    "-0.00043659",
    "0",
    1691603879999,
    "0",
    12,
    "0",
    "0",
    "0"
  ]
]
```

---

## 14. 最新标记价格和资金费率

**接口描述**
采集各大交易所数据加权平均。

**HTTP请求**
`GET /fapi/v1/premiumIndex`

**请求权重**
1

**请求参数**
| 名称 | 类型 | 是否必需 | 描述 |
| :--- | :--- | :--- | :--- |
| symbol | STRING | NO | 交易对，不发送则返回所有交易对数据 |

**响应示例 (指定 symbol)**
```json
{
    "symbol": "BTCUSDT",
    "markPrice": "11793.63104562",
    "indexPrice": "11781.80495970",
    "estimatedSettlePrice": "11781.16138815",
    "lastFundingRate": "0.00038246",
    "interestRate": "0.00010000",
    "nextFundingTime": 1597392000000,
    "time": 1597370495002
}
```

**响应示例 (不指定 symbol)**
```json
[
    {
        "symbol": "BTCUSDT",
        "markPrice": "11793.63104562",
        "indexPrice": "11781.80495970",
        "estimatedSettlePrice": "11781.16138815",
        "lastFundingRate": "0.00038246",
        "interestRate": "0.00010000",
        "nextFundingTime": 1597392000000,
        "time": 1597370495002
    }
]
```

---

## 15. 查询资金费率历史

**接口描述**
查询资金费率历史。如果 startTime 和 endTime 都未发送, 返回最近 limit 条数据。如果 startTime 和 endTime 之间的数据量大于 limit, 返回 startTime + limit 情况下的数据。

**HTTP请求**
`GET /fapi/v1/fundingRate`

**请求权重**
和`GET /fapi/v1/fundingInfo`共享 500/5min/IP

**请求参数**
| 名称 | 类型 | 是否必需 | 描述 |
| :--- | :--- | :--- | :--- |
| symbol | STRING | NO | 交易对 |
| startTime | LONG | NO | 起始时间 |
| endTime | LONG | NO | 结束时间 |
| limit | INT | NO | 默认值:100 最大值:1000 |

**响应示例**
```json
[
	{
    	"symbol": "BTCUSDT",
    	"fundingRate": "-0.03750000",
    	"fundingTime": 1570608000000,
        "markPrice": "34287.54619963"
	},
	{
   		"symbol": "BTCUSDT",
    	"fundingRate": "0.00010000",
    	"fundingTime": 1570636800000,
        "markPrice": "34287.54619963"
	}
]
```

---

## 16. 查询资金费率信息

**接口描述**
查询资金费率信息，接口仅返回 FundingRateCap/FundingRateFloor/fundingIntervalHours 等被特殊调整过的交易对，没调整过的不返回。

**HTTP请求**
`GET /fapi/v1/fundingInfo`

**请求权重**
0 (和 `GET /fapi/v1/fundingRate` 共享 500/5min/IP)

**请求参数**
无

**响应示例**
```json
[
    {
        "symbol": "BLZUSDT",
        "adjustedFundingRateCap": "0.02500000",
        "adjustedFundingRateFloor": "-0.02500000",
        "fundingIntervalHours": 8,
        "disclaimer": false
    }
]
```

---

## 17. 24hr价格变动情况

**接口描述**
请注意，不携带 symbol 参数会返回全部交易对数据，不仅数据庞大，而且权重极高。

**HTTP请求**
`GET /fapi/v1/ticker/24hr`

**请求权重**
带 symbol 为 1, 不带为 40

**请求参数**
| 名称 | 类型 | 是否必需 | 描述 |
| :--- | :--- | :--- | :--- |
| symbol | STRING | NO | 交易对，不发送则返回所有交易对信息 |

**响应示例 (指定 symbol)**
```json
{
  "symbol": "BTCUSDT",
  "priceChange": "-94.99999800",
  "priceChangePercent": "-95.960",
  "weightedAvgPrice": "0.29628482",
  "lastPrice": "4.00000200",
  "lastQty": "200.00000000",
  "openPrice": "99.00000000",
  "highPrice": "100.00000000",
  "lowPrice": "0.10000000",
  "volume": "8913.30000000",
  "quoteVolume": "15.30000000",
  "openTime": 1499783499040,
  "closeTime": 1499869899040,
  "firstId": 28385,
  "lastId": 28460,
  "count": 76
}
```

**响应示例 (不指定 symbol)**
```json
[
    {
        "symbol": "BTCUSDT",
        "priceChange": "-94.99999800",
        "priceChangePercent": "-95.960",
        "weightedAvgPrice": "0.29628482",
        "lastPrice": "4.00000200",
        "lastQty": "200.00000000",
        "openPrice": "99.00000000",
        "highPrice": "100.00000000",
        "lowPrice": "0.10000000",
        "volume": "8913.30000000",
        "quoteVolume": "15.30000000",
        "openTime": 1499783499040,
        "closeTime": 1499869899040,
        "firstId": 28385,
        "lastId": 28460,
        "count": 76
    }
]
```

---

## 18. 最新价格

**接口描述**
返回最近价格。

**HTTP请求**
`GET /fapi/v1/ticker/price`

**请求权重**
单交易对 1，无交易对 2

**请求参数**
| 名称 | 类型 | 是否必需 | 描述 |
| :--- | :--- | :--- | :--- |
| symbol | STRING | NO | 交易对，不发送则返回所有交易对信息 |

**响应示例 (指定 symbol)**
```json
{
  "symbol": "LTCBTC",
  "price": "4.00000200",
  "time": 1589437530011
}
```

**响应示例 (不指定 symbol)**
```json
[
    {
        "symbol": "BTCUSDT",
        "price": "6000.01",
        "time": 1589437530011
    }
]
```

---

## 19. 最新价格V2

**接口描述**
返回最近价格。该接口返回头中的 `X-MBX-USED-WEIGHT-1M` 参数不准确，可以忽略。

**HTTP请求**
`GET /fapi/v2/ticker/price`

**请求权重**
单交易对 1，无交易对 2

**请求参数**
| 名称 | 类型 | 是否必需 | 描述 |
| :--- | :--- | :--- | :--- |
| symbol | STRING | NO | 交易对，不发送则返回所有交易对信息 |

**响应示例 (指定 symbol)**
```json
{
  "symbol": "LTCBTC",
  "price": "4.00000200",
  "time": 1589437530011
}
```

**响应示例 (不指定 symbol)**
```json
[
    {
        "symbol": "BTCUSDT",
        "price": "6000.01",
        "time": 1589437530011
    }
]
```

---

## 20. 当前最优挂单

**接口描述**
返回当前最优的挂单(最高买单，最低卖单)。该接口返回头中的 `X-MBX-USED-WEIGHT-1M` 参数不准确，可以忽略。

**HTTP请求**
`GET /fapi/v1/ticker/bookTicker`

**请求权重**
单交易对 2，无交易对 5

**请求参数**
| 名称 | 类型 | 是否必需 | 描述 |
| :--- | :--- | :--- | :--- |
| symbol | STRING | NO | 交易对，不发送则返回所有交易对信息 |

**响应示例 (指定 symbol)**
```json
{
  "symbol": "BTCUSDT",
  "bidPrice": "4.00000000",
  "bidQty": "431.00000000",
  "askPrice": "4.00000200",
  "askQty": "9.00000000",
  "time": 1589437530011
}
```

**响应示例 (不指定 symbol)**
```json
[
    {
        "symbol": "BTCUSDT",
        "bidPrice": "4.00000000",
        "bidQty": "431.00000000",
        "askPrice": "4.00000200",
        "askQty": "9.00000000",
        "time": 1589437530011
    }
]
```

---

## 21. 季度合约历史结算价

**接口描述**
返回季度合约历史结算价。

**HTTP请求**
`GET /futures/data/delivery-price`

**请求权重**
0

**请求参数**
| 名称 | 类型 | 是否必需 | 描述 |
| :--- | :--- | :--- | :--- |
| pair | STRING | YES | 如 BTCUSDT |

**响应示例**
```json
[
    {
        "deliveryTime": 1695945600000,
        "deliveryPrice": "27103.00000000"
    },
    {
        "deliveryTime": 1688083200000,
        "deliveryPrice": "30733.60000000"
    }
]
```

---

## 22. 获取未平仓合约数

**接口描述**
获取未平仓合约数。

**HTTP请求**
`GET /fapi/v1/openInterest`

**请求权重**
1

**请求参数**
| 名称 | 类型 | 是否必需 | 描述 |
| :--- | :--- | :--- | :--- |
| symbol | STRING | YES | 交易对 |

**响应示例**
```json
{
	"openInterest": "10659.509",
	"symbol": "BTCUSDT",
	"time": 1589437530011
}
```

---

## 23. 合约持仓量历史

**接口描述**
查询合约持仓量历史。若无 startTime 和 endTime 限制，则默认返回当前时间往前的 limit 值。仅支持最近1个月的数据。IP限频为1000次/5min。

**HTTP请求**
`GET /futures/data/openInterestHist`

**请求权重**
0

**请求参数**
| 名称 | 类型 | 是否必需 | 描述 |
| :--- | :--- | :--- | :--- |
| symbol | STRING | YES | 交易对 |
| period | ENUM | YES | "5m","15m","30m","1h","2h","4h","6h","12h","1d" |
| limit | LONG | NO | default 30, max 500 |
| startTime | LONG | NO | 起始时间 |
| endTime | LONG | NO | 结束时间 |

**响应示例**
```json
[
    {
         "symbol":"BTCUSDT",
	     "sumOpenInterest":"20403.12345678",
	     "sumOpenInterestValue": "176196512.12345678",
	     "timestamp":"1583127900000"
     },
     {
         "symbol":"BTCUSDT",
         "sumOpenInterest":"20401.36700000",
         "sumOpenInterestValue":"149940752.14464448",
         "timestamp":"1583128200000"
     }
]
```

---

## 24. 大户持仓量多空比

**接口描述**
大户的多头和空头总持仓量占比，大户指保证金余额排名前20%的用户。
多仓持仓量比例 = 大户多仓持仓量 / 大户总持仓量
空仓持仓量比例 = 大户空仓持仓量 / 大户总持仓量
多空持仓量比值 = 多仓持仓量比例 / 空仓持仓量比例
若无 startTime 和 endTime 限制，则默认返回当前时间往前的 limit 值。
仅支持最近30天的数据。
IP限频为1000次/5min。

**HTTP请求**
`GET /futures/data/topLongShortPositionRatio`

**请求权重**
0

**请求参数**
| 名称 | 类型 | 是否必需 | 描述 |
| :--- | :--- | :--- | :--- |
| symbol | STRING | YES | 交易对 |
| period | ENUM | YES | "5m","15m","30m","1h","2h","4h","6h","12h","1d" |
| limit | LONG | NO | default 30, max 500 |
| startTime | LONG | NO | 起始时间 |
| endTime | LONG | NO | 结束时间 |

**响应示例**
```json
[
    {
         "symbol":"BTCUSDT",
	     "longShortRatio":"1.4342",
	     "longAccount": "0.5344",
	     "shortAccount":"0.4238",
	     "timestamp":"*************"
     },
     {
         "symbol":"BTCUSDT",
	     "longShortRatio":"1.4337",
	     "longAccount": "0.5891",
	     "shortAccount":"0.4108",
	     "timestamp":"*************"
     }
]
```

---

## 25. 大户账户数多空比

**接口描述**
持仓大户的净持仓多头和空头账户数占比，大户指保证金余额排名前20%的用户。一个账户记一次。
多仓账户数比例 = 持多仓大户数 / 总持仓大户数
空仓账户数比例 = 持空仓大户数 / 总持仓大户数
多空账户数比值 = 多仓账户数比例 / 空仓账户数比例
若无 startTime 和 endTime 限制，则默认返回当前时间往前的 limit 值。
仅支持最近30天的数据。
IP限频为1000次/5min。

**HTTP请求**
`GET /futures/data/topLongShortAccountRatio`

**请求参数**
| 名称 | 类型 | 是否必需 | 描述 |
| :--- | :--- | :--- | :--- |
| symbol | STRING | YES | 交易对 |
| period | ENUM | YES | "5m","15m","30m","1h","2h","4h","6h","12h","1d" |
| limit | LONG | NO | default 30, max 500 |
| startTime | LONG | NO | 起始时间 |
| endTime | LONG | NO | 结束时间 |

**响应示例**
```json
[
    {
         "symbol":"BTCUSDT",
	     "longShortRatio":"1.8105",
	     "longAccount": "0.6442",
	     "shortAccount":"0.3558",
	     "timestamp":"*************"
    },
    {
         "symbol":"BTCUSDT",
	     "longShortRatio":"1.8233",
	     "longAccount": "0.5338",
	     "shortAccount":"0.3454",
	     "timestamp":"*************"
	}
]
```

---

## 26. 多空持仓人数比

**接口描述**
多空持仓人数比。若无 startTime 和 endTime 限制，则默认返回当前时间往前的 limit 值。仅支持最近30天的数据。IP限频为1000次/5min。

**HTTP请求**
`GET /futures/data/globalLongShortAccountRatio`

**请求权重**
0

**请求参数**
| 名称 | 类型 | 是否必需 | 描述 |
| :--- | :--- | :--- | :--- |
| symbol | STRING | YES | 交易对 |
| period | ENUM | YES | "5m","15m","30m","1h","2h","4h","6h","12h","1d" |
| limit | LONG | NO | default 30, max 500 |
| startTime | LONG | NO | 起始时间 |
| endTime | LONG | NO | 结束时间 |

**响应示例**
```json
[
    {
         "symbol":"BTCUSDT",
	     "longShortRatio":"0.1960",
	     "longAccount": "0.6622",
	     "shortAccount":"0.3378",
	     "timestamp":"*************"
     },
     {
         "symbol":"BTCUSDT",
	     "longShortRatio":"1.9559",
	     "longAccount": "0.6617",
	     "shortAccount":"0.3382",
	     "timestamp":"*************"
     }
]
```

---

## 27. 合约主动买卖量

**接口描述**
合约主动买卖量。若无 startTime 和 endTime 限制，则默认返回当前时间往前的 limit 值。仅支持最近30天的数据。IP限频为1000次/5min。

**HTTP请求**
`GET /futures/data/takerlongshortRatio`

**请求权重**
0

**请求参数**
| 名称 | 类型 | 是否必需 | 描述 |
| :--- | :--- | :--- | :--- |
| symbol | STRING | YES | 交易对 |
| period | ENUM | YES | "5m","15m","30m","1h","2h","4h","6h","12h","1d" |
| limit | LONG | NO | default 30, max 500 |
| startTime | LONG | NO | 起始时间 |
| endTime | LONG | NO | 结束时间 |

**响应示例**
```json
[
  {
    "buySellRatio": "1.5586",
    "buyVol": "387.3300",
    "sellVol": "248.5030",
    "timestamp": "*************"
  },
  {
    "buySellRatio": "1.3104",
    "buyVol": "343.9290",
    "sellVol": "248.5030",
    "timestamp": "*************"
  }
]
```

---

## 28. 基差

**接口描述**
查询期货基差。若无 startTime 和 endTime 限制，则默认返回当前时间往前的 limit 值。仅支持最近30天的数据。

**HTTP请求**
`GET /futures/data/basis`

**请求权重**
0

**请求参数**
| 名称 | 类型 | 是否必需 | 描述 |
| :--- | :--- | :--- | :--- |
| pair | STRING | YES | BTCUSDT |
| contractType | ENUM | YES | CURRENT_QUARTER, NEXT_QUARTER, PERPETUAL |
| period | ENUM | YES | "5m","15m","30m","1h","2h","4h","6h","12h","1d" |
| limit | LONG | YES | Default 30, Max 500 |
| startTime | LONG | NO | 起始时间 |
| endTime | LONG | NO | 结束时间 |

**响应示例**
```json
[
    {
        "indexPrice": "34400.15945055",
        "contractType": "PERPETUAL",
        "basisRate": "0.0004",
        "futuresPrice": "34414.10",
        "annualizedBasisRate": "",
        "basis": "13.94054945",
        "pair": "BTCUSDT",
        "timestamp": 1698742800000
    }
]
```

---

## 29. 综合指数交易对信息

**接口描述**
获取交易对为综合指数的基础成分信息。

**HTTP请求**
`GET /fapi/v1/indexInfo`

**请求权重**
1

**请求参数**
| 名称 | 类型 | 是否必需 | 描述 |
| :--- | :--- | :--- | :--- |
| symbol | STRING | NO | 交易对 |

**响应示例**
```json
[
	{
		"symbol": "DEFIUSDT",
		"time": 1589437530011,
		"component": "baseAsset",
		"baseAssetList":[
			{
				"baseAsset":"BAL",
				"quoteAsset": "USDT",
				"weightInQuantity":"1.04406228",
				"weightInPercentage":"0.02783900"
			},
			{
				"baseAsset":"BAND",
				"quoteAsset": "USDT",
				"weightInQuantity":"3.53782729",
				"weightInPercentage":"0.03935200"
			}
		]
	}
]
```

---

## 30. 多资产模式资产汇率指数

**接口描述**
多资产模式资产汇率指数。

**HTTP请求**
`GET /fapi/v1/assetIndex`

**请求权重**
单个 symbol 权重为 1；省略 symbol 参数时权重为 10。

**请求参数**
| Name | Type | Mandatory | Description |
| :--- | :--- | :--- | :--- |
| symbol | STRING | NO | Asset pair |

**响应示例 (指定 symbol)**
```json
{
    "symbol": "ADAUSD",
    "time": 1635740268004,
    "index": "1.92957370",
    "bidBuffer": "0.10000000",
    "askBuffer": "0.10000000",
    "bidRate": "1.73661633",
    "askRate": "2.12253107",
    "autoExchangeBidBuffer": "0.05000000",
    "autoExchangeAskBuffer": "0.05000000",
    "autoExchangeBidRate": "1.83309501",
    "autoExchangeAskRate": "2.02605238"
}
```

**响应示例 (不指定 symbol)**
```json
[
    {
        "symbol": "ADAUSD",
        "time": 1635740268004,
        "index": "1.92957370",
        "bidBuffer": "0.10000000",
        "askBuffer": "0.10000000",
        "bidRate": "1.73661633",
        "askRate": "2.12253107",
        "autoExchangeBidBuffer": "0.05000000",
        "autoExchangeAskBuffer": "0.05000000",
        "autoExchangeBidRate": "1.83309501",
        "autoExchangeAskRate": "2.02605238"
    }
]
```

---

## 31. 查询指数价格成分

**接口描述**
查询指数价格成分。

**HTTP请求**
`GET /fapi/v1/constituents`

**请求权重**
2

**请求参数**
| 名称 | 类型 | 是否必需 | 描述 |
| :--- | :--- | :--- | :--- |
| symbol | STRING | YES | 交易对 |

**响应示例**
```json
{
    "symbol": "BTCUSDT",
    "time": 1745401553408,
    "constituents": [
        {
            "exchange": "binance",
            "symbol": "BTCUSDT",
            "price": "94057.03000000",
            "weight": "0.51282051"
        },
        {
            "exchange": "coinbase",
            "symbol": "BTC-USDT",
            "price": "94140.58000000",
            "weight": "0.15384615"
        }
    ]
}
```

---

## 32. 查询保险基金余额快照

**接口描述**
查询保险基金余额快照。

**HTTP请求**
`GET /fapi/v1/insuranceBalance`

**请求权重**
1

**请求参数**
| 名称 | 类型 | 是否必需 | 描述 |
| :--- | :--- | :--- | :--- |
| symbol | STRING | NO | 交易对 |

**响应示例 (指定 symbol)**
```json
{
   "symbols":[
      "BNBUSDT",
      "BTCUSDT",
      "ETHUSDT"
   ],
   "assets":[
      {
         "asset":"USDT",
         "marginBalance":"793930579.315848",
         "updateTime":1745366402000
      },
      {
         "asset":"BTC",
         "marginBalance":"61.73143554",
         "updateTime":1745366402000
      }
   ]
}
```

**响应示例 (不指定 symbol)**
```json
[
   {
      "symbols":[
         "ADAUSDT",
         "BCHUSDT",
         "DOTUSDT"
      ],
      "assets":[
         {
            "asset":"USDT",
            "marginBalance":"314151411.06482935",
            "updateTime":1745366402000
         }
      ]
   },
   {
      "symbols":[
         "ACTUSDT",
         "MUBARAKUSDT",
         "OMUSDT"
      ],
      "assets":[
         {
            "asset":"USDT",
            "marginBalance":"5166686.84431694",
            "updateTime":1745366402000
         }
      ]
   }
]
```