#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试批量获取订单簿数据功能
"""

import sys
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from api_requests.binance.binance_orderbook_requester import BinanceOrderbookRequester

def test_batch_futures():
    """测试批量获取合约订单簿"""
    print("🚀 测试批量获取合约订单簿数据...")
    
    requester = BinanceOrderbookRequester()
    
    # 测试小批量获取（5个交易对，批量大小2）
    result = requester.fetch_all_active_futures_orderbooks(
        max_symbols=5,
        batch_size=2,
        limit=100
    )
    
    print(f"📊 批量获取结果: {result}")
    return result

def test_batch_spot():
    """测试批量获取现货订单簿"""
    print("\n🚀 测试批量获取现货订单簿数据...")
    
    requester = BinanceOrderbookRequester()
    
    # 测试小批量获取（3个交易对，批量大小2）
    result = requester.fetch_all_active_spot_orderbooks(
        max_symbols=3,
        batch_size=2,
        limit=100
    )
    
    print(f"📊 批量获取结果: {result}")
    return result

def test_single_orderbook():
    """测试单个订单簿获取"""
    print("\n🚀 测试单个订单簿获取...")
    
    requester = BinanceOrderbookRequester()
    
    # 测试合约订单簿
    print("测试BTCUSDT合约订单簿...")
    futures_result = requester.fetch_futures_orderbook("BTCUSDT", 100)
    print(f"合约结果: {futures_result}")
    
    # 测试现货订单簿
    print("测试ETHUSDT现货订单簿...")
    spot_result = requester.fetch_spot_orderbook("ETHUSDT", 100)
    print(f"现货结果: {spot_result}")
    
    return futures_result and spot_result

if __name__ == "__main__":
    print("=" * 60)
    print("🧪 开始测试币安订单簿批量获取功能")
    print("=" * 60)
    
    # 测试单个获取
    single_success = test_single_orderbook()
    
    # 测试批量获取
    futures_result = test_batch_futures()
    spot_result = test_batch_spot()
    
    print("\n" + "=" * 60)
    print("📋 测试总结:")
    print(f"✅ 单个获取: {'成功' if single_success else '失败'}")
    print(f"✅ 批量合约: {'成功' if futures_result.get('success') else '失败'}")
    print(f"✅ 批量现货: {'成功' if spot_result.get('success') else '失败'}")
    
    if futures_result.get('success'):
        print(f"📊 合约成功率: {futures_result.get('success_rate', 0):.1f}%")
    
    if spot_result.get('success'):
        print(f"📊 现货成功率: {spot_result.get('success_rate', 0):.1f}%")
    
    print("=" * 60)
