#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试监控功能 - 短时间运行
"""

import sys
import time
import threading
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from api_requests.binance.binance_orderbook_requester import BinanceOrderbookRequester

def test_monitoring_short():
    """测试监控功能 - 短时间运行"""
    print("🚀 测试监控功能（10秒后自动停止）...")
    
    requester = BinanceOrderbookRequester()
    
    # 创建一个定时器来停止监控
    def stop_monitoring():
        print("\n⏰ 10秒测试时间到，停止监控...")
        import os
        os._exit(0)
    
    timer = threading.Timer(10.0, stop_monitoring)
    timer.start()
    
    try:
        # 临时修改配置为更小的数量和更短的间隔
        from config import BINANCE_ORDERBOOK_CONFIG
        original_config = BINANCE_ORDERBOOK_CONFIG.copy()
        
        # 修改为测试配置
        BINANCE_ORDERBOOK_CONFIG["update_interval_seconds"] = 5  # 5秒间隔
        BINANCE_ORDERBOOK_CONFIG["max_futures_symbols"] = 3      # 只获取3个合约
        BINANCE_ORDERBOOK_CONFIG["max_spot_symbols"] = 2         # 只获取2个现货
        BINANCE_ORDERBOOK_CONFIG["futures_batch_size"] = 2       # 批量大小2
        BINANCE_ORDERBOOK_CONFIG["spot_batch_size"] = 2          # 批量大小2
        
        print("📊 测试配置:")
        print(f"  ⏱️ 更新间隔: {BINANCE_ORDERBOOK_CONFIG['update_interval_seconds']} 秒")
        print(f"  📈 合约数量: {BINANCE_ORDERBOOK_CONFIG['max_futures_symbols']}")
        print(f"  📉 现货数量: {BINANCE_ORDERBOOK_CONFIG['max_spot_symbols']}")
        print("=" * 50)
        
        # 启动监控
        requester.start_continuous_monitoring()
        
    except KeyboardInterrupt:
        print("\n🛑 用户中断测试")
    except Exception as e:
        print(f"❌ 测试失败: {e}")
    finally:
        timer.cancel()

if __name__ == "__main__":
    test_monitoring_short()
