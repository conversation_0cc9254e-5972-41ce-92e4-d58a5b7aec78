# 币安K线数据收集器

一个高效、可靠的币安期货K线数据收集和存储系统，支持所有交易对的实时数据获取、存储和维护。

## 功能特性

- 🚀 **高性能异步数据获取**：支持批量并发请求，智能频率控制
- 💾 **混合存储方案**：TimescaleDB + Parquet文件双重存储
- 🔄 **自动数据更新**：支持历史数据收集和实时数据更新
- 🛡️ **数据质量保证**：内置数据验证、清理和监控功能
- 📊 **多时间周期**：支持1分钟到1周的所有K线周期
- 🔧 **灵活配置**：YAML配置文件，支持自定义各种参数
- 📈 **数据分析友好**：提供Pandas DataFrame接口

## 系统架构

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   币安API       │───▶│  数据获取器       │───▶│  数据存储       │
│  (REST API)     │    │ (异步批量请求)    │    │ (TimescaleDB)   │
└─────────────────┘    └──────────────────┘    └─────────────────┘
                                │                        │
                                ▼                        ▼
                       ┌──────────────────┐    ┌─────────────────┐
                       │  频率限制器       │    │  文件备份       │
                       │ (智能调节)        │    │ (Parquet)       │
                       └──────────────────┘    └─────────────────┘
```

## 项目结构

```
Project/
├── binance_klines_fetcher.py    # 币安K线数据获取器
├── klines_storage.py            # 数据存储模块
├── klines_collector.py          # 主收集器程序
├── data_maintenance.py          # 数据维护和监控
├── example_usage.py             # 使用示例
├── config.yaml                  # 配置文件
├── setup_database.sql           # 数据库初始化脚本
├── requirements.txt             # 依赖包列表
├── README.md                    # 项目文档
├── 币安api.md                   # 币安API文档
│
├── data/klines/                 # Parquet文件存储
│   ├── 2024-01-01/
│   │   ├── BTCUSDT_1h.parquet
│   │   ├── ETHUSDT_1h.parquet
│   │   └── ...
│   └── 2024-01-02/
│       └── ...
│
├── backups/                     # 备份文件
│   ├── BTCUSDT_1h_20240101_120000.parquet.gz
│   └── ...
│
└── logs/                        # 日志文件
    ├── klines_collector.log
    └── ...
```

## 快速开始

### 1. 环境准备

```bash
# 克隆项目
git clone <repository-url>
cd binance-klines-collector

# 安装依赖
pip install -r requirements.txt
```

### 2. 数据库设置（可选但推荐）

安装PostgreSQL和TimescaleDB：

```bash
# Ubuntu/Debian
sudo apt-get install postgresql postgresql-contrib
sudo apt-get install timescaledb-postgresql

# 或使用Docker
docker run -d --name timescaledb -p 5432:5432 -e POSTGRES_PASSWORD=password timescale/timescaledb:latest-pg14
```

创建数据库：

```bash
# 连接到PostgreSQL
psql -U postgres -h localhost

# 执行初始化脚本
\i setup_database.sql
```

## 🔧 安装和配置

### 1. 环境要求

- Python 3.7+
- 网络连接

### 2. 安装依赖

```bash
cd Project
pip install -r requirements.txt
```

### 3. 配置API密钥

编辑 `config.py` 文件或设置环境变量：

```bash
# CoinGlass API密钥
export COINGLASS_API_KEY="your_coinglass_api_key"

# Google AI API密钥
export GOOGLE_AI_API_KEY="your_google_ai_api_key"
```

## 🚀 快速开始

### 调度器模式（默认）

```bash
# 启动调度器模式 - 每60秒执行一次，永久运行
python main.py

# 自定义执行间隔（秒）
python main.py --interval 120  # 每2分钟执行一次
python main.py --interval 300  # 每5分钟执行一次
```

### 手动模式

```bash
# 手动模式 - 执行一次后退出
python main.py --manual

# 单次执行模式 - 禁用调度器
python main.py --no-scheduler

# 运行指定的API请求器（手动模式）
python main.py --manual --run binance_get_futures_exchange_info
```

### 其他功能

```bash
# 查看所有可用的API请求器
python main.py --list

# 查看数据存储统计
python main.py --stats
```

### 📊 调度器模式详细说明

#### 🔄 执行流程
1. **启动时立即执行**: 程序启动后立即执行一次所有API请求器
2. **定时循环执行**: 按设定间隔（默认60秒）持续执行
3. **并发处理**: 6个API请求器同时运行，提高效率
4. **实时统计**: 显示每次执行的结果和累计统计

#### ⚙️ 配置选项
- **默认间隔**: 60秒（可在config.py中修改`SCHEDULER_CONFIG["interval_seconds"]`）
- **命令行覆盖**: 使用`--interval`参数临时修改间隔
- **永久运行**: 持续运行直到手动停止（Ctrl+C）
- **容错机制**: 单个请求器失败不影响其他请求器和整体调度

#### 📈 执行统计示例
```
🔄 开始第 1 次数据收集 (2025-07-17 07:51:30)
✅ 第 1 次执行完成: 全部成功 (6/6)
⏰ 下次执行时间: 2025-07-17 07:52:30

🔄 开始第 2 次数据收集 (2025-07-17 07:52:30)
✅ 第 2 次执行完成: 全部成功 (6/6)
⏰ 下次执行时间: 2025-07-17 07:53:30
```

### 单独运行某个API请求器

```bash
python api_requests/binance/binance_get_futures_exchange_info.py
```

## 📡 API接口详细说明

### 🏦 币安 (Binance) API

#### 1. 合约交易所信息 (`binance_get_futures_exchange_info`) ✅
- **功能**: 获取币安合约交易所规则和交易对信息
- **URL**: `https://fapi.binance.com/fapi/v1/exchangeInfo`
- **方法**: GET | **权重**: 1 | **参数**: 无
- **数据量**: 533个交易对信息
- **数据包含**: 交易对列表、价格精度、数量精度、交易规则、过滤器等
- **更新频率**: 每次调度器执行时更新
- **状态**: ✅ 正常运行

#### 2. 合约资金费率 (`binance_get_futures_premium_index`) ✅
- **功能**: 获取永续合约资金费率和标记价格
- **URL**: `https://fapi.binance.com/fapi/v1/premiumIndex`
- **方法**: GET | **权重**: 1 | **参数**: `symbol` (可选)
- **数据量**: 538个交易对的资金费率数据
- **数据包含**: 标记价格、指数价格、资金费率、下次结算时间等
- **更新频率**: 每次调度器执行时更新
- **状态**: ✅ 正常运行

### 🔍 CoinGlass API

#### 1. V4合约市场数据-币安 (`coinglass_get_v4_futures_binance_markets`) ✅
- **功能**: 分页获取币安合约市场综合数据
- **URL**: `https://open-api-v4.coinglass.com/api/futures/coins-markets`
- **方法**: GET | **认证**: `CG-API-KEY` header
- **参数**: `exchange_list=Binance`, `page`, `per_page`
- **数据量**: 452个币种，约5页数据
- **数据包含**: 币安交易所的价格、持仓量、成交量、资金费率等
- **更新频率**: 每次调度器执行时更新
- **状态**: ✅ 正常运行

#### 2. V4合约全市场数据 (`coinglass_get_v4_futures_all_markets`) ✅
- **功能**: 分页获取所有交易所的合约市场综合数据
- **URL**: `https://open-api-v4.coinglass.com/api/futures/coins-markets`
- **方法**: GET | **认证**: `CG-API-KEY` header
- **参数**: `page`, `per_page` (不限制交易所)
- **数据量**: 834个币种，约9页数据
- **数据包含**: 全市场合约价格、持仓量、成交量、资金费率等
- **更新频率**: 每次调度器执行时更新
- **状态**: ✅ 正常运行

#### 3. V4现货全市场数据 (`coinglass_get_v4_spot_all_markets`) ✅
- **功能**: 分页获取所有交易所的现货市场综合数据
- **URL**: `https://open-api-v4.coinglass.com/api/spot/coins-markets`
- **方法**: GET | **认证**: `CG-API-KEY` header
- **参数**: `page`, `per_page` (不限制交易所)
- **数据量**: 1044个币种，约11页数据
- **数据包含**: 全市场现货价格、成交量、市值等数据
- **更新频率**: 每次调度器执行时更新
- **状态**: ✅ 正常运行

### 🤖 Google AI API

#### 1. Gemini内容生成 (`api_requests/google_ai/google_ai_generate_gemini_content.py`) ✅
- **功能**: 调用Google Gemini AI生成分析内容
- **URL**: `https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash:generateContent`
- **方法**: POST
- **认证**: `X-goog-api-key` header
- **参数**:
  - `prompt`: 输入提示词 (必填)
  - `api_key`: API密钥 (可选，使用配置默认值)
- **缓存时间**: 30分钟
- **数据包含**: 原始提示词、生成内容、完整API响应
- **状态**: 当前可用

## ⚙️ 配置说明

### 主要配置项

- **BINANCE_CONFIG**: 币安API配置，包含URL、端点、速率限制等
- **COINGLASS_CONFIG**: CoinGlass API配置，包含V4和Legacy API设置
- **GOOGLE_AI_CONFIG**: Google AI配置，包含多个备用API密钥
- **DATA_STORAGE_CONFIG**: 数据存储配置，包含缓存时间、清理策略等

### 速率限制

- **币安API**: 1200请求/分钟，2400权重/分钟
- **CoinGlass API**: 60请求/分钟
- **Google AI API**: 根据具体配额限制

## 🔍 数据存储

### 存储结构

每个API请求器都有独立的存储目录，文件命名格式：
```
{timestamp}_{api_identifier}.json
```

### 数据格式

每个数据文件包含：
```json
{
  "timestamp": "2025-07-14T23:05:10.123456+08:00",
  "api_identifier": "binance_get_futures_exchange_info",
  "metadata": {
    "server_time": 1626285910123,
    "symbols_count": 150,
    "request_url": "https://fapi.binance.com/fapi/v1/exchangeInfo"
  },
  "data": {
    // 原始API响应数据
  }
}
```

### 文件管理策略

- **清理功能**: 已禁用自动清理，所有数据文件永久保留
- **文件统计**: 启用详细的文件统计信息显示
- **存储策略**: 无文件数量限制，无时间限制
- **配置灵活**: 可在config.py中重新启用清理功能

## 📊 系统性能统计

### 🚀 执行性能
- **总API请求器**: 6个
- **并发执行时间**: ~15秒
- **成功率**: 100% (6/6)
- **数据获取总量**: 每次执行获取约2,900+条数据记录

### 📈 数据量统计
| API来源 | 请求器数量 | 数据量 | 执行时间 |
|---------|------------|--------|----------|
| 币安 (Binance) | 2个 | 533+538=1,071条 | ~3秒 |
| CoinGlass | 3个 | 452+834+1,044=2,330条 | ~12秒 |
| Google AI | 1个 | 自动跳过 | ~2秒 |
| **总计** | **6个** | **~3,400条** | **~15秒** |

### 🎯 当前API请求器详情

#### 🏦 币安 (Binance) API (2个)
1. **binance_get_futures_exchange_info** - 533个合约交易对信息
2. **binance_get_futures_premium_index** - 538个交易对资金费率

#### 🔍 CoinGlass API (3个)
3. **coinglass_get_v4_futures_binance_markets** - 452个币安合约数据
4. **coinglass_get_v4_futures_all_markets** - 834个全市场合约数据
5. **coinglass_get_v4_spot_all_markets** - 1,044个全市场现货数据

#### 🤖 Google AI API (1个)
6. **google_ai_generate_gemini_content** - Gemini内容生成（需参数）

### 🔧 系统优化历程
- ✅ 修复了币安API的参数传递问题
- ✅ 实现了调度器模式，支持永久运行
- ✅ 集成所有工具类到main.py，简化架构
- ✅ 禁用自动清理，永久保留历史数据
- ✅ 添加详细的文件统计和执行监控
- ✅ 完善错误处理和重试机制

## 🚦 工作流程

### 单点执行
```bash
python api_requests/some_api.py
```

### 批量并发执行
```bash
python main.py --run
```

1. 自动扫描 `api_requests/` 目录
2. 识别所有可执行的请求器
3. 使用 `multiprocessing` 并发启动请求器
4. 每个请求器独立完成数据获取和存储
5. 输出统计结果和错误报告

## 🛠️ 开发指南

### 添加新的API请求器

1. 在 `api_requests/` 目录创建新的Python文件
2. 继承基本的请求器模式：

```python
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
新API请求器描述
"""

import sys
from pathlib import Path

project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from main import FileManager, HTTPClient, setup_logger
from config import YOUR_API_CONFIG

class YourAPIRequester:
    def __init__(self):
        self.api_identifier = "your_api_identifier"
        self.logger = setup_logger(f"api_requests.{self.api_identifier}")
        self.file_manager = FileManager()
        self.http_client = HTTPClient()

    def fetch_data(self) -> bool:
        # 实现你的API调用逻辑
        pass

def main():
    requester = YourAPIRequester()
    success = requester.fetch_data()
    exit(0 if success else 1)

if __name__ == "__main__":
    main()
```

3. 在 `config.py` 中添加API配置
4. 运行测试确保正常工作

### 扩展功能

- 在 `main.py` 中添加新的工具函数（所有工具类已集成）
- 更新 `config.py` 添加新的配置项
- 在 `main.py` 中添加新的命令行选项
- 所有工具类（FileManager、HTTPClient、Logger）都在main.py中

## 📈 监控和维护

### 日志

- 控制台输出：实时显示执行状态
- 文件日志：详细记录到 `logs/` 目录
- 日志轮转：自动管理日志文件大小

### 错误处理

- 网络超时重试
- API限制智能等待
- 数据验证和容错
- 详细错误报告

### 性能优化

- 连接池复用
- 并发执行控制
- 内存使用优化
- 磁盘空间管理

## 💡 使用示例和最佳实践

### 🚀 生产环境部署
```bash
# 1. 启动调度器模式（推荐）
python main.py

# 2. 自定义执行间隔（根据需求调整）
python main.py --interval 300  # 每5分钟执行一次

# 3. 使用nohup在后台运行
nohup python main.py > output.log 2>&1 &
```

### 📊 数据监控
```bash
# 查看实时统计
python main.py --stats

# 查看所有API请求器状态
python main.py --list

# 测试单个API请求器
python main.py --manual --run binance_get_futures_exchange_info
```

### 🔧 配置优化建议
1. **执行间隔**: 根据数据更新频率调整，建议60-300秒
2. **并发数**: 默认6个并发已优化，无需修改
3. **存储空间**: 定期监控磁盘使用情况
4. **网络稳定**: 确保网络连接稳定，避免API请求失败

### 📈 数据分析建议
- 币安数据：适合实时交易分析
- CoinGlass数据：适合市场趋势分析
- 历史数据：可用于回测和模型训练

## 🤝 贡献指南

1. Fork项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 发起Pull Request

## 📄 许可证

本项目采用MIT许可证，详见LICENSE文件。

## 🎯 项目状态总结

### ✅ 当前状态
- **系统状态**: 🟢 完全正常运行
- **API请求器**: 6个全部正常工作
- **成功率**: 100% (6/6)
- **数据获取**: 每次执行约3,400条数据记录
- **执行效率**: 并发执行仅需15秒

### 🚀 主要功能
- ✅ 调度器模式：支持永久运行和定时执行
- ✅ 并发处理：6个API请求器同时运行
- ✅ 数据管理：永久保留所有历史数据
- ✅ 错误处理：完善的重试和容错机制
- ✅ 实时监控：详细的执行统计和文件管理

### 📊 数据覆盖
- **币安**: 533个合约交易对 + 538个资金费率
- **CoinGlass**: 452个币安合约 + 834个全市场合约 + 1,044个全市场现货
- **更新频率**: 默认每60秒更新一次

**🎉 系统已完全优化，可用于生产环境！**

## 📞 支持

如有问题或建议，请通过以下方式联系：

- 提交Issue
- 发起Discussion
- 邮件联系

---

**注意**: 使用本框架前请确保已获得相应API的使用权限，并遵守各API服务商的使用条款。
